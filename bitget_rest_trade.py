import hmac
import base64
import json
import time
import requests

# 配置
API_KEY = "bg_a1b170432edacb846b2c507296e410f1"
SECRET_KEY = "3e22fb6cd5ee4a221521113964a0b79c2c967ba1af75059089b062f50990f205"
PASSPHRASE = "1234qwer"
BASE_URL = "https://api.bitget.com"


def get_timestamp() -> str:
    """获取13位毫秒时间戳"""
    return str(int(time.time() * 1000))


def to_query_string(params: dict) -> str:
    """构建 GET 请求的 query string，按 key 排序，不进行 URL 编码"""
    if not params:
        return ''
    sorted_params = sorted(params.items())
    return '?' + '&'.join([f"{k}={v}" for k, v in sorted_params])


def sign_request(timestamp: str, method: str, request_path: str, body: str, secret_key: str) -> str:
    """生成签名字符串"""
    prehash_string = timestamp + method.upper() + request_path + body
    mac = hmac.new(secret_key.encode('utf-8'), prehash_string.encode('utf-8'), digestmod='sha256')
    return base64.b64encode(mac.digest()).decode()


def build_headers(timestamp: str, signature: str) -> dict:
    """生成请求头"""
    return {
        "ACCESS-KEY": API_KEY,
        "ACCESS-SIGN": signature,
        "ACCESS-TIMESTAMP": timestamp,
        "ACCESS-PASSPHRASE": PASSPHRASE,
        "Content-Type": "application/json",
        "locale": "en-US"
    }


def send_request(method: str, path: str, params=None, data=None):
    """发送请求，自动处理签名和 headers"""
    timestamp = get_timestamp()

    # GET 请求处理 query string
    if method.upper() == "GET":
        query_str = to_query_string(params or {})
        full_path = path + query_str
        body_str = ""
    else:
        full_path = path
        body_str = json.dumps(data or {}, separators=(',', ':'))

    # 生成签名
    signature = sign_request(timestamp, method, full_path, body_str, SECRET_KEY)
    headers = build_headers(timestamp, signature)

    url = BASE_URL + full_path

    # 发出请求
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        else:
            response = requests.post(url, headers=headers, data=body_str)
        return response.json()
    except Exception as e:
        return {"error": str(e)}


def get_account():
    response = send_request(
        method="GET",
        path="/api/v2/mix/account/account",
        params={
            "symbol": "BTCUSDT",
            "marginCoin": "USDT",
            "productType": "USDT-FUTURES"
        }
    )
    return response


def create_order(symbol, price, amount, side, trade_side):
    response = send_request(method="POST",
                            path="/api/v2/mix/order/place-order",
                            data={"productType": "USDT-FUTURES",
                                  "symbol": symbol,
                                  "marginMode": "crossed",
                                  "marginCoin": "USDT",
                                  "price": str(price),
                                  "size": str(amount),
                                  "side": side,
                                  "orderType": "limit",
                                  "force": "gtc",
                                  "tradeSide": trade_side
                                  }
                            )
    return response


def get_position(symbol):
    response = send_request(method="GET",
                            path="/api/v2/mix/position/single-position",
                            params={"productType": "USDT-FUTURES",
                                    "symbol": symbol,
                                    "marginCoin": "USDT"
                                    }
                            )
    return response


def get_tick(symbol):
    response = send_request(method="GET",
                            path="/api/v2/mix/market/ticker",
                            params={"productType": "USDT-FUTURES",
                                    "symbol": symbol
                                    }
                            )
    return response


def get_open_orders(symbol):
    response = send_request(method="GET",
                            path="/api/v2/mix/order/orders-pending",
                            params={"productType": "USDT-FUTURES",
                                    "symbol": symbol
                                    }
                            )
    return response


def cancel_all_orders(symbol):
    response = send_request(method="POST",
                            path="/api/v2/mix/order/batch-cancel-orders",
                            data={"productType": "USDT-FUTURES",
                                  "symbol": symbol
                                  }
                            )
    return response


def bitget_strategy(symbol, long_sgnl, short_sgnl):
    tick = get_tick(symbol="BTCUSDT")['data'][0]
    bid1, ask1 = float(tick['askPr']), float(tick['bidPr'])
    size = 0.0001
    if long_sgnl:
        print('bitget:')
        open_long = create_order(symbol=symbol, price=ask1, amount=size, side='buy', trade_side='open')
        print(open_long)
        close_short = create_order(symbol=symbol, price=ask1, amount=size, side='sell', trade_side='close')
        print(close_short)
    if short_sgnl:
        print('bitget:')
        open_short = create_order(symbol=symbol, price=bid1, amount=size, side='sell', trade_side='open')
        print(open_short)
        close_long = create_order(symbol=symbol, price=bid1, amount=size, side='buy', trade_side='close')
        print(close_long)

# print(get_position(symbol="BTCUSDT"))
# print(get_open_orders(symbol="BTCUSDT"))
# print(cancel_all_orders(symbol="BTCUSDT"))
