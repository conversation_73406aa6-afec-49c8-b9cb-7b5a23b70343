import base64
import datetime
import json
import time
import pandas as pd
import requests
import hmac
from hashlib import sha256


# ok_simu
api = "ef2a67a7-3dcd-4447-951e-8b5ebcfc6cb0"
secret = "DEFB998329DB8CDC60AA7165884DAE02"
password = "Kk11cc00.."
base_url = 'https://www.okx.com'

okx_symbol = 'BTC-USDT-SWAP'
swap_mode = 'cross'  # 全仓
swap_leverage = 100  # 杠杆倍数
okx_size = 2


def send_discord_message(message):
    data = {"content": message}
    headers = {"Content-Type": "application/json"}
    response = requests.post(
        url='https://discord.com/api/webhooks/1383476987514982440/vDCzmas7DlpinF_oGACeduqT8uzcpPYKfPmqnNT410rDsinDfMLVoo1e7QYuB22WhC70',
        data=json.dumps(data),
        headers=headers
    )
    return response.status_code


def get_timestamp():
    response = requests.get('https://www.okx.com/api/v5/public/time').json()
    if response['code'] == '0':
        ts = response['data'][0]['ts']
        dt = datetime.datetime.fromtimestamp(int(ts) / 1000, tz=datetime.timezone.utc)
        ts = dt.strftime('%Y-%m-%dT%H:%M:%SZ')
    else:
        dt = datetime.datetime.now(datetime.timezone.utc)
        ts = dt.strftime('%Y-%m-%dT%H:%M:%SZ')
    return ts


def get_ticker(symbol):
    request_path = '/api/v5/public/mark-price'
    url = base_url + request_path
    params = {'instType': 'SWAP', 'instId': symbol}
    response = requests.get(url, params)
    return response.json()


# print(get_ticker(symbol=swap_symbol))


def get_orderbook(symbol):
    request_path = '/api/v5/market/ticker'
    url = base_url + request_path
    params = {'instType': 'SWAP', 'instId': symbol}
    response = requests.get(url, params).json()
    if response['code'] == '0':
        order_book = response['data'][0]
    else:
        order_book = None
    return order_book


# print(get_orderbook(swap_symbol))


def signature(timestamp, method, request_path, body, secret_key):
    if body is None:
        body = ''
    message = f"{timestamp}{method.upper()}{request_path}{body}"
    mac = hmac.new(secret_key.encode(), message.encode(), digestmod=sha256)
    return base64.b64encode(mac.digest()).decode()


def get_header(api_key, timestamp, method, request_path, passphrase, body, secret_key):
    sign = signature(timestamp, method, request_path, body, secret_key)
    return {
        'Content-Type': 'application/json',
        'OK-ACCESS-KEY': api_key,
        'OK-ACCESS-SIGN': sign,
        'OK-ACCESS-TIMESTAMP': timestamp,
        'OK-ACCESS-PASSPHRASE': passphrase,
        'x-simulated-trading': '1'  # 模拟盘
    }


# def get_account_info():
#     request_path = "/api/v5/account/config"
#     timestamp = get_timestamp()
#     headers = get_header(api, timestamp, 'GET', request_path, password, None, secret)
#     url = base_url + request_path
#     response = requests.get(url, headers=headers)
#     return response.json()
#
#
# def get_leverage(symbol, mode):
#     request_path = f"/api/v5/account/leverage-info?instId={symbol}&mgnMode={mode}"
#     timestamp = get_timestamp()
#     headers = get_header(api, timestamp, 'GET', request_path, password, None, secret)
#     url = base_url + request_path
#     response = requests.get(url, headers=headers)
#     return response.json()
#
#
# # print(get_leverage(symbol=swap_symbol, mode=swap_mode))
#
#
# def set_leverage(symbol, leverage, mode):
#     request_path = '/api/v5/account/set-leverage'
#     timestamp = get_timestamp()
#     body = json.dumps({"instId": symbol, "lever": leverage, "mgnMode": mode})
#     headers = get_header(api, timestamp, 'POST', request_path, password, body, secret)
#     url = base_url + request_path
#     response = requests.post(url, headers=headers, data=body)
#     data = response.json()['data'][0]
#     if int(data['lever']) == 5 and data['mgnMode'] == 'cross':
#         print('已设置5倍杠杆，全仓模式')
#     else:
#         print('设置杠杆失败，请手动设置')
#     return response.json()
#
#
# # print(set_leverage(symbol=swap_symbol, leverage=swap_leverage, mode=swap_mode))
#
#
# def set_position_mode():
#     pos_mode = get_account_info()['data'][0]['posMode']
#     if pos_mode == 'net_mode':
#         print('账户模式为单向持仓模式，无需设置')
#     else:
#         request_path = f"/api/v5/account/set-position-mode"
#         timestamp = get_timestamp()
#         body = json.dumps({"posMode": "long_short_mode"})
#         headers = get_header(api, timestamp, 'POST', request_path, password, body, secret)
#         url = base_url + request_path
#         response = requests.post(url, headers=headers, data=body)
#         return response.json()
#
#
# # print(set_position_mode())


def get_balance(currency):
    request_path = f"/api/v5/account/balance?ccy={currency}"
    timestamp = get_timestamp()
    headers = get_header(api, timestamp, 'GET', request_path, password, None, secret)
    url = base_url + request_path
    response = requests.get(url, headers=headers).json()
    if response['code'] == '0':
        balance = response['data'][0]['details'][0].get('eq', 0)
    else:
        balance = 0
    return balance


# print(get_balance('USDT'))  # ['data'][0]['details'][0].get('eq', 0)


def get_position(symbol):
    long_posi, short_posi, long_urppct, short_urppct = 0, 0, 0, 0
    request_path = f"/api/v5/account/positions?instId={symbol}"
    timestamp = get_timestamp()
    headers = get_header(api, timestamp, 'GET', request_path, password, None, secret)
    url = base_url + request_path
    posi = requests.get(url, headers=headers).json()
    if posi['code'] == '0':
        positions = posi['data']
        if len(positions) > 0:
            for position in positions:
                pos_side = position.get('posSide')
                pos = int(position['pos'])
                if pos_side == 'long' and pos != 0:
                    long_posi = pos
                    long_urppct = float(position['uplRatio'])
                elif pos_side == 'short' and pos != 0:
                    short_posi = pos
                    short_urppct = float(position['uplRatio'])
    return long_posi, short_posi, long_urppct, short_urppct


# print(get_position(swap_symbol))


def open_limit_order(symbol, mode, side, price, sz, reduce_only, pos_side=None,
                     tpTriggerPx=None, tpOrdPx=None, slTriggerPx=None, slOrdPx=None):
    request_path = '/api/v5/trade/order'
    timestamp = get_timestamp()
    order_data = {"instId": symbol,
                  "tdMode": mode,
                  "side": side,
                  "ordType": "limit",
                  "px": price,
                  "sz": sz,
                  "reduceOnly": reduce_only
                  }
    if pos_side:
        order_data["posSide"] = pos_side
    if tpTriggerPx and tpOrdPx:
        order_data["tpTriggerPx"] = str(tpTriggerPx)  # 注意，OKX要求是字符串
        order_data["tpOrdPx"] = str(tpOrdPx)
    if slTriggerPx and slOrdPx:
        order_data["slTriggerPx"] = str(slTriggerPx)
        order_data["slOrdPx"] = str(slOrdPx)
    body = json.dumps(order_data)
    headers = get_header(api, timestamp, 'POST', request_path, password, body, secret)
    url = base_url + request_path
    response = requests.post(url, headers=headers, data=body).json()
    return response


def open_market_order(symbol, mode, side, sz, reduce_only, pos_side=None,
                      tpTriggerPx=None, tpOrdPx=None, slTriggerPx=None, slOrdPx=None):
    request_path = '/api/v5/trade/order'
    timestamp = get_timestamp()
    order_data = {"instId": symbol,
                  "tdMode": mode,
                  "side": side,
                  "ordType": "market",
                  "sz": sz,
                  "reduceOnly": reduce_only
                  }
    if pos_side:
        order_data["posSide"] = pos_side
    if tpTriggerPx and tpOrdPx:
        order_data["tpTriggerPx"] = str(tpTriggerPx)
        order_data["tpOrdPx"] = str(tpOrdPx)
    if slTriggerPx and slOrdPx:
        order_data["slTriggerPx"] = str(slTriggerPx)
        order_data["slOrdPx"] = str(slOrdPx)
    body = json.dumps(order_data)
    headers = get_header(api, timestamp, 'POST', request_path, password, body, secret)
    url = base_url + request_path
    response = requests.post(url, headers=headers, data=body).json()
    return response


def get_order_info(symbol, order_id):
    request_path = f"/api/v5/trade/order?ordId={order_id}&instId={symbol}"
    timestamp = get_timestamp()
    headers = get_header(api, timestamp, 'GET', request_path, password, None, secret)
    url = base_url + request_path
    response = requests.get(url, headers=headers)
    return response.json()


def fetch_open_orders(symbol):
    open_orders = []
    request_path = f"/api/v5/trade/orders-pending?instId={symbol}&instType=SWAP"
    timestamp = get_timestamp()
    headers = get_header(api, timestamp, 'GET', request_path, password, None, secret)
    url = base_url + request_path
    response = requests.get(url, headers=headers).json()
    if response['code'] == '0':
        open_orders = response['data']
    else:
        print(f"获取未成交订单失败, 原因: {response['msg']}")
    return open_orders


# print(fetch_open_orders(symbol=swap_symbol))


def cancel_order(symbol, order_id):
    request_path = '/api/v5/trade/cancel-order'
    timestamp = get_timestamp()
    body = json.dumps({"ordId": order_id,
                       "instId": symbol,
                       })
    headers = get_header(api, timestamp, 'POST', request_path, password, body, secret)
    url = base_url + request_path
    response = requests.post(url, headers=headers, data=body).json()
    return response


def fetch_open_algo_orders(symbol):
    request_path = f"/api/v5/trade/orders-algo-pending?instId={symbol}&ordType=conditional,oco&instType=SWAP"
    timestamp = get_timestamp()
    headers = get_header(api, timestamp, 'GET', request_path, password, None, secret)
    url = base_url + request_path
    response = requests.get(url, headers=headers).json()
    if response['code'] == '0':
        open_algo_orders = response['data']
    else:
        print(f"获取未成交策略单失败, 原因: {response['msg']}")
        open_algo_orders = []
    return open_algo_orders


# print(fetch_open_algo_orders(symbol=swap_symbol))


def cancel_algo_order(symbol, algo_id):
    request_path = "/api/v5/trade/cancel-algos"
    timestamp = get_timestamp()
    order_data = [{
        "algoId": algo_id,
        "instId": symbol
    }]
    body = json.dumps(order_data)
    headers = get_header(api, timestamp, 'POST', request_path, password, body, secret)
    url = base_url + request_path
    response = requests.post(url, headers=headers, data=body).json()
    if response['code'] == '0':
        print(f"成功撤销止盈止损单: algoId={algo_id}")
    else:
        print(f"撤销止盈止损单失败, 原因: {response['msg']}")
    return response


# print(fetch_open_algo_orders(swap_symbol))
# cancel_algo_order(swap_symbol, fetch_open_algo_orders(swap_symbol)[0]['algoId'])


def okx_strategy(symbol, long_signal, short_signal):
    order_book = get_orderbook(symbol)
    ask1 = float(order_book['askPx'])
    bid1 = float(order_book['bidPx'])
    balance = get_balance('USDT')
    long_posi, short_posi, _, _ = get_position(symbol)
    if long_signal:
        open_long_order = open_limit_order(symbol=symbol, mode=swap_mode, side='buy',
                                           pos_side='long', sz=okx_size, reduce_only=False, price=ask1)
        close_short_order = open_limit_order(symbol=symbol, mode=swap_mode, side='buy',
                                             pos_side='short', sz=okx_size, reduce_only=True, price=ask1)
        # print(open_long_order)
        # print(close_short_order)
        msg_cn = '交易信号: 以{}开多{}BTC, 多仓{}'.format(ask1, okx_size, long_posi)
        send_discord_message(msg_cn)

    if short_signal:
        open_short_order = open_limit_order(symbol=symbol, mode=swap_mode, side='sell',
                                            pos_side='short', sz=okx_size, reduce_only=False, price=bid1)
        close_long_order = open_limit_order(symbol=symbol, mode=swap_mode, side='sell',
                                            pos_side='long', sz=okx_size, reduce_only=True, price=bid1)
        # print(open_short_order)
        # print(close_long_order)
        msg_cn = '交易信号: 以{}开空{}BTC, 空仓{}'.format(bid1, okx_size, short_posi)
        send_discord_message(msg_cn)

