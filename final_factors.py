import numpy as np
import pandas as pd
import functools


def fill_window(window=100):
    '''装饰器，对因子进行信号填充'''
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            signals = func(*args, **kwargs)  # 调用原函数获取 Series

            s_modified = pd.Series(0, index=signals.index)  # 初始化全0

            idx_1 = signals[signals == 1].index
            idx_neg1 = signals[signals == -1].index

            s_pos = pd.Series(0, index=signals.index)
            s_neg = pd.Series(0, index=signals.index)

            for i in idx_1:
                # 获取时间戳 i 在 signals 中的位置索引
                pos = signals.index.get_loc(i)
                s_pos.iloc[pos:pos + window] = 1  # 让后续100个点变成 1

            for i in idx_neg1:
                # 获取时间戳 i 在 signals 中的位置索引
                pos = signals.index.get_loc(i)
                s_neg.iloc[pos:pos + window] = -1  # 让后续100个点变成 -1

            s_modified = s_pos + s_neg
            return s_modified

        return wrapper
    return decorator


def fill_window_800(window=800):
    '''适用于lib填充的装饰器'''
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            s = func(*args, **kwargs)
            if not isinstance(s, pd.Series):
                raise TypeError("The function must return a pandas Series")

            s_modified = s.copy()
            idx_1 = s[s == 1].index
            idx_neg1 = s[s == -1].index

            for i in idx_neg1:
                pos = s.index.get_loc(i)
                s_modified.iloc[pos:pos+window] = -1
            for i in idx_1:
                pos = s.index.get_loc(i)
                s_modified.iloc[pos:pos+window] = 1

            return s_modified
        return wrapper
    return decorator


class FinalFactor:
    """因子调度器，包含允许调用的安全方法"""

    @staticmethod
    def factor_sample_1(d):
        """示例因子1"""
        c = d.close
        long_T = 20
        short_T = 5
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        s = ma_short / ma_long

        return s

    @staticmethod
    def factor_sample_2(d):
        """示例因子2"""
        c = d.close
        long_T = 30
        short_T = 10
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        s = ma_short / ma_long

        return s

    @fill_window_800()
    @staticmethod
    def d_lib001(d):
        c = d.close

        # 计算AO指标
        fast_ma = c.rolling(window=5).mean()  # 快速移动平均线（5周期）
        slow_ma = c.rolling(window=34).mean()  # 慢速移动平均线（34周期）
        ao = fast_ma - slow_ma  # AO = 快速MA - 慢速MA

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # AO由负变正产生买入信号
        for i in range(1, len(ao)):
            if ao[i - 1] < 0 and ao[i] > 0:  # AO由负变正
                buy_signal[i] = 1  # 产生买入信号

            # AO由正变负产生卖出信号
            elif ao[i - 1] > 0 and ao[i] < 0:  # AO由正变负
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib002(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # 布林带和 RSI 的标准计算周期
        rsi_period = 14  # RSI 的计算周期

        # 计算 RSI
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 计算布林带
        rolling_mean = c.rolling(window=period).mean()
        rolling_std = c.rolling(window=period).std()
        lower_band = rolling_mean - (rolling_std * 2)  # 下轨
        upper_band = rolling_mean + (rolling_std * 2)  # 上轨

        # 买入信号：价格触及布林带下轨且 RSI 低于 30（超卖）
        buy_signal = ((c <= lower_band) & (rsi < 30)).astype(int)

        # 卖出信号：价格触及布林带上轨且 RSI 高于 70（超买）
        sell_signal = ((c >= upper_band) & (rsi > 70)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal - sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib003(d):
        c = d.close
        high = d.high
        low = d.low

        # 计算CCI（Commodity Channel Index）
        period = 14
        typical_price = (high + low + c) / 3  # 典型价格
        sma = typical_price.rolling(window=period).mean()  # 典型价格的简单移动平均
        mean_deviation = (
            typical_price - sma).abs().rolling(window=period).mean()  # 平均偏差
        cci = (typical_price - sma) / (0.015 * mean_deviation)  # CCI公式

        # 计算斐波那契回撤水平
        fib_levels = [0.236, 0.382, 0.5, 0.618]  # 常见的斐波那契回撤水平
        fib_retracements = pd.DataFrame(index=c.index)

        # 计算每个周期的高低价差
        high_max = high.rolling(window=period).max()
        low_min = low.rolling(window=period).min()

        # 计算斐波那契回撤位
        fib_retracements['fib_23_6'] = high_max - \
            (high_max - low_min) * fib_levels[0]
        fib_retracements['fib_38_2'] = high_max - \
            (high_max - low_min) * fib_levels[1]
        fib_retracements['fib_50_0'] = high_max - \
            (high_max - low_min) * fib_levels[2]
        fib_retracements['fib_61_8'] = high_max - \
            (high_max - low_min) * fib_levels[3]

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(c)):
            # 如果CCI大于100且当前价格接近斐波那契回撤23.6%或者38.2%
            if cci[i] > 100 and (abs(c[i] - fib_retracements['fib_23_6'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                 abs(c[i] - fib_retracements['fib_38_2'][i]) < 0.02 * (high_max[i] - low_min[i])):
                sell_signal[i] = -1  # 卖出信号

            # 如果CCI小于-100且当前价格接近斐波那契回撤61.8%或者50%
            elif cci[i] < -100 and (abs(c[i] - fib_retracements['fib_61_8'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                    abs(c[i] - fib_retracements['fib_50_0'][i]) < 0.02 * (high_max[i] - low_min[i])):
                buy_signal[i] = 1  # 买入信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        return s

    @fill_window_800()
    @staticmethod
    def d_lib004(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @fill_window_800()
    @staticmethod
    def d_lib005(d):
        c = d.close
        long_T = 30  # 长期移动平均的周期
        short_T = 5  # 短期DPO的周期
        ma_T = 10  # 用来判断趋势的移动平均周期

        # 计算 DPO 值：当前价格 - 长期移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        dpo = c - ma_short  # DPO是当前价格与短期移动平均的差值

        # 计算MA值来判断趋势方向
        ma = c.rolling(window=ma_T).mean()  # 用ma_T来计算一个中期的简单移动平均

        # 买卖信号：
        # 1. DPO值为正并且价格高于MA时，认为是买入信号
        # 2. DPO值为负并且价格低于MA时，认为是卖出信号
        buy_signal = (dpo > 0) & (c > ma)
        sell_signal = (dpo < 0) & (c < ma)

        # 输出信号，买入信号为1，卖出信号为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @fill_window_800()
    @staticmethod
    def d_lib006(d):
        c = d.close
        # 计算指数加权移动平均（EMA）
        ema_period = 14
        ema = c.ewm(span=ema_period, adjust=False).mean()

        # 归一化ENA指标
        max_ema = ema.max()  # 计算EMA的最大值
        min_ema = ema.min()  # 计算EMA的最小值
        ena = (ema - min_ema) / (max_ema - min_ema)  # 归一化ENA

        # 计算ENA的信号线（EMA的移动平均）
        ena_signal = ena.ewm(span=9, adjust=False).mean()  # 9周期的EMA作为信号线

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # ENA由下向上穿越信号线时产生买入信号
        for i in range(1, len(ena)):
            if ena[i - 1] < ena_signal[i - 1] and ena[i] > ena_signal[i]:  # ENA由下向上穿越信号线
                buy_signal[i] = 1  # 产生买入信号

            # ENA由上向下穿越信号线时产生卖出信号
            elif ena[i - 1] > ena_signal[i - 1] and ena[i] < ena_signal[i]:  # ENA由上向下穿越信号线
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib007(d):
        c = d.close
        high = d.high
        low = d.low

        # 设置参数
        kc_period = 20  # 均线计算周期
        atr_period = 14  # ATR计算周期
        atr_multiplier = 2  # ATR倍数

        # 计算20日均线
        ma = c.rolling(window=kc_period).mean()

        # 计算ATR
        tr1 = high - low
        tr2 = (high - c.shift(1)).abs()
        tr3 = (low - c.shift(1)).abs()
        true_range = tr1.combine(tr2, max).combine(tr3, max)
        atr = true_range.rolling(window=atr_period).mean()

        # 计算Keltner Channel上下轨
        kc_upper = ma + (atr * atr_multiplier)  # KC上轨
        kc_lower = ma - (atr * atr_multiplier)  # KC下轨

        # 生成买入和卖出信号
        buy_signal = (c < kc_lower).astype(int)  # 当价格跌破KC下轨时买入
        sell_signal = (c > kc_upper).astype(int)  # 当价格突破KC上轨时卖出

        # 合并信号：1为买入，-1为卖出，0为中性
        signals = buy_signal - sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib008(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                 lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                 upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd

        return s

    @fill_window_800()
    @staticmethod
    def d_lib009(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 CCI
        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        cci_20 = compute_rsi(d, 20)

        # 创建买入信号
        condu = ((c > ma120) & (cci_20 < -100) & (c > c.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (cci_20 > 100) & (c < c.shift(1))).astype(int)

        s = condu - condd

        return s

    @fill_window_800()
    @staticmethod
    def d_lib010(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 创建买入信号
        condu = ((c > ma120) & (macd > 0) & (macd > signal) &
                 (macd.shift(1) <= signal.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (macd < 0) & (macd < signal) &
                 (macd.shift(1) >= signal.shift(1))).astype(int)

        s = condu - condd

        return s

    @fill_window_800()
    @staticmethod
    def d_lib011(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd

        return s

    @fill_window_800()
    @staticmethod
    def d_lib012(d):
        c = d.close  # Get closing prices
        short_T = 12  # Short-term EMA period
        long_T = 26   # Long-term EMA period
        signal_T = 9  # Signal line period (MACD signal line)

        # Calculate short-term and long-term EMAs
        ema_short = c.ewm(span=short_T, adjust=False).mean()
        ema_long = c.ewm(span=long_T, adjust=False).mean()

        # Calculate DIF (Difference between short-term and long-term EMAs)
        dif = ema_short - ema_long

        # Calculate MACD (Signal line is an EMA of the DIF)
        macd = dif.ewm(span=signal_T, adjust=False).mean()

        # Calculate the difference between closing price and MACD for bar height
        bar = c - macd

        # Detect Golden Cross (DIF crosses above MACD)
        golden_cross = ((dif > macd) & (
            dif.shift(1) <= macd.shift(1))).astype(int)

        # Detect Death Cross (DIF crosses below MACD)
        death_cross = ((dif < macd) & (
            dif.shift(1) >= macd.shift(1))).astype(int)

        # Detect shrinking bars (current bar is smaller than the previous one)
        bar_shrink = (bar.abs() < bar.shift(1).abs()).astype(int)

        # Buy signal: Golden cross with shrinking green bars
        buy_signal = golden_cross & bar_shrink

        # Sell signal: Death cross with shrinking red bars
        sell_signal = death_cross & bar_shrink

        # Return buy and sell signals
        return buy_signal - sell_signal  # 1 for buy, -1 for sell, 0 for no action

    @fill_window_800()
    @staticmethod
    def d_lib013(d):
        c = d.close
        period_short = 12  # 快速EMA的周期
        period_long = 26   # 慢速EMA的周期
        signal_period = 9  # 信号线的周期

        # 计算快速和慢速EMA
        ema_short = c.ewm(span=period_short, adjust=False).mean()
        ema_long = c.ewm(span=period_long, adjust=False).mean()

        # 计算 MACD 线
        macd_line = ema_short - ema_long

        # 计算信号线
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()

        # 买入信号：MACD 线向上穿越信号线
        golden_cross_buy = ((macd_line.shift(1) < signal_line.shift(1)) & (
            macd_line >= signal_line)).astype(int)

        # 卖出信号：MACD 线向下穿越信号线
        death_cross_sell = ((macd_line.shift(1) > signal_line.shift(1)) & (
            macd_line <= signal_line)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = golden_cross_buy - death_cross_sell

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib014(d):
        c = d.close      # 收盘价
        v = d.volume     # 成交量
        long_T = 30      # 长期均线窗口
        short_T = 5      # 短期均线窗口
        kdj_period = 14  # KDJ指标的计算周期
        bbi_short_T = 5  # BBI的短期均线周期
        bbi_long_T = 10  # BBI的长期均线周期

        # 计算短期和长期的移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算KDJ指标
        low_min = c.rolling(window=kdj_period).min()
        high_max = c.rolling(window=kdj_period).max()
        rsv = 100 * (c - low_min) / (high_max - low_min)  # RSV值
        k = rsv.ewm(com=2, adjust=False).mean()  # K值
        d = k.ewm(com=2, adjust=False).mean()  # D值
        j = 3 * k - 2 * d  # J值

        # KDJ金叉条件：K线突破D线且KDJ值小于25
        kdj_cross_up = (k > d) & (k.shift(1) <= d.shift(1))  # KDJ金叉
        kdj_below_25 = j < 25  # KDJ值小于25
        buy_signal_kdj = kdj_cross_up & kdj_below_25

        # KDJ死叉条件：D线突破K线且KDJ值大于75
        kdj_cross_down = (k < d) & (k.shift(1) >= d.shift(1))  # KDJ死叉
        kdj_above_75 = j > 75  # KDJ值大于75
        sell_signal_kdj = kdj_cross_down & kdj_above_75

        # 计算BBI（买卖平衡指数）
        ma_5 = c.rolling(window=5).mean()
        ma_10 = c.rolling(window=10).mean()
        ma_20 = c.rolling(window=20).mean()
        bbi = (ma_5 + ma_10 + ma_20) / 3  # BBI = (5日均线 + 10日均线 + 20日均线) / 3

        # BBI突破双底颈线：BBI突破前期低点
        bbi_double_bottom = bbi > bbi.shift(1)  # BBI突破前一日

        # 买入信号：MA在50之上且KDJ金叉且KDJ小于25，同时BBI突破双底颈线
        buy_signal_bbi = (ma_short > 50) & buy_signal_kdj & bbi_double_bottom

        # 卖出信号：KDJ死叉且KDJ大于75
        sell_signal_bbi = sell_signal_kdj

        # 合并信号：1表示买入，-1表示卖出，0表示没有信号
        signals = buy_signal_bbi.astype(int) - sell_signal_bbi.astype(int)

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib015(d):
        """
        Generates trading signals based on the MA level and CCI threshold:
        - Long entry if MA is above 50 and CCI > 100.
        - Short entry if MA is below 50 and CCI < -100.
        """
        close = d.close
        high = d.high
        low = d.low

        # Parameters
        ma_period = 50  # Moving Average period for trend detection
        cci_period = 20  # CCI period (commonly used)
        cci_threshold = 100  # Threshold for CCI

        # Calculate MA
        ma = close.rolling(window=ma_period).mean()

        # Calculate CCI
        tp = (high + low + close) / 3  # Typical Price for CCI calculation
        cci = (tp - tp.rolling(window=cci_period).mean()) / \
            (0.015 * tp.rolling(window=cci_period).std())

        # Generate Signals
        long_entry = ((ma > 50) & (cci > cci_threshold)).astype(int)
        short_entry = ((ma < 50) & (cci < -cci_threshold)).astype(int)

        # Signal output: 1 for long, -1 for short
        signals = long_entry - short_entry

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib016(d):
        c = d.close
        long_T1 = 30  # 长期平均线1
        long_T2 = 60  # 长期平均线2
        short_T = 5   # 短期平均线

        # 计算短期和长期移动平均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long1 = c.rolling(window=long_T1).mean()
        ma_long2 = c.rolling(window=long_T2).mean()

        # 确保长期移动平均线大于50
        ma_condition = (ma_long1 > 50) & (ma_long2 > 50)

        # 判断短期平均线是否上穿长期平均线
        crossover_signal1 = (ma_short > ma_long1) & (
            ma_short.shift(1) <= ma_long1.shift(1))
        crossover_signal2 = (ma_short > ma_long2) & (
            ma_short.shift(1) <= ma_long2.shift(1))

        # 结合条件，产生最终信号
        signal = ma_condition.astype(
            int) * (crossover_signal1 | crossover_signal2).astype(int)

        return signal

    @fill_window_800()
    @staticmethod
    def d_lib017(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

        # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

        # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = golden_cross_buy - death_cross_sell

        return signals  # 返回信号序列

    @fill_window_800()
    @staticmethod
    def d_lib018(d, short_period=9, long_period=26):
        c = d.close

        # 计算短期和长期 EMA
        ema_short = c.ewm(span=short_period, adjust=False).mean()
        ema_long = c.ewm(span=long_period, adjust=False).mean()

        # 计算 PO
        po = (ema_short - ema_long) / ema_long * 100

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(po)):
            # PO 上穿 0 时，产生买入信号
            if po[i - 1] <= 0 and po[i] > 0:
                buy_signal[i] = 1  # 买入信号

            # PO 下穿 0 时，产生卖出信号
            elif po[i - 1] >= 0 and po[i] < 0:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib019(d):
        c = d.close
        high = d.high
        low = d.low

        # Parameters for Bollinger Bands and RSI
        rsi_period = 14
        boll_period = 20
        boll_std = 2

        # Calculate the RSI (Relative Strength Index)
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Calculate Bollinger Bands
        rolling_mean = c.rolling(window=boll_period).mean()
        rolling_std = c.rolling(window=boll_period).std()
        boll_upper = rolling_mean + (boll_std * rolling_std)
        boll_lower = rolling_mean - (boll_std * rolling_std)
        boll_middle = rolling_mean

        # Buy signal condition: RSI crosses above 50 and price is above the middle Bollinger Band
        buy_signal = ((rsi.shift(1) <= 50) & (rsi > 50) & (c > boll_middle))

        # Sell signal condition: Price breaks below the middle Bollinger Band and reaches the lower Bollinger Band
        sell_signal = ((c.shift(1) > boll_middle) & (
            c < boll_middle) & (c <= boll_lower))

        # Signal output: 1 for buy, -1 for sell, 0 for no action
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @fill_window_800()
    @staticmethod
    def d_lib020(d):
        c = d.close
        td_buy_count = 0
        td_sell_count = 0
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # TD 买入结构
        for i in range(4, len(c)):
            # 买入结构
            if c[i] < c[i - 4]:  # 当前收盘价低于4天前收盘价
                td_buy_count += 1
            else:
                td_buy_count = 0  # 重置计数

            # 满足连续9天买入条件
            if td_buy_count == 9:
                buy_signal[i] = 1  # 产生买入信号
                td_buy_count = 0  # 重置计数器

            # 卖出结构
            if c[i] > c[i - 4]:  # 当前收盘价高于4天前收盘价
                td_sell_count += 1
            else:
                td_sell_count = 0  # 重置计数

            # 满足连续9天卖出条件
            if td_sell_count == 9:
                sell_signal[i] = -1  # 产生卖出信号
                td_sell_count = 0  # 重置计数器

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @fill_window_800()
    @staticmethod
    def d_lib021(d, period=14):
        c = d.close  # 获取收盘价

        # 计算威廉指标（Williams %R）
        highest_high = d.high.rolling(window=period).max()  # 过去14天的最高价
        lowest_low = d.low.rolling(window=period).min()     # 过去14天的最低价
        williams_r = -100 * (highest_high - c) / \
            (highest_high - lowest_low)  # Williams %R 计算

        # 买入信号：威廉指标低于-80
        buy_signal = williams_r < -80

        # 卖出信号：威廉指标高于-20
        sell_signal = williams_r > -20

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal.astype(int) - sell_signal.astype(int)

        return signals  # 返回信号序列

    @staticmethod
    def c_chu001(df):
        '''衡量当前波动率高低的过滤器'''
        log_ratio = np.log(df['close'] / df['close'].shift(1))
        hv = log_ratio.rolling(20).std()
        return hv

    @staticmethod
    def c_chu002(df):
        '''衡量当前成交量高低的过滤器'''
        volume_mean = df['volume'].rolling(20).mean()
        volume_deviation = (df['volume'] - volume_mean) / volume_mean
        return volume_deviation

    @staticmethod
    def c_chu003(df):
        '''衡量当前相对位置高低的过滤器'''
        up = df['high'].rolling(20).max()
        down = df['low'].rolling(20).min()
        price_position = (df['close'] - down) / (up - down)
        return price_position

    @staticmethod
    def c_chu004(df):
        '''衡量短期价格波动快慢的过滤器'''
        std_5 = df['close'].rolling(5).std()
        std_30 = df['close'].rolling(30).std()
        price_fluctuation = std_5 / std_30
        return price_fluctuation

    @staticmethod
    def c_chu005(df, window=14):
        '''计算atr因子'''
        tr1 = df['high'] - df['low']
        tr2 = abs(df['high'] - df['close'].shift(1))
        tr3 = abs(df['close'].shift(1) - df['low'])
        tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)
        atr = tr.ewm(alpha=1 / window, adjust=False).mean()
        return atr

    @staticmethod
    def c_chu006(df, window=14):
        '''计算rsi因子'''
        close_diff = df['close'].diff()
        u = np.where(close_diff > 0, close_diff, 0)
        d = np.where(close_diff < 0, -close_diff, 0)
        smma_u = pd.Series(u).ewm(alpha=1 / window, adjust=False).mean()
        smma_d = pd.Series(d).ewm(alpha=1 / window, adjust=False).mean()
        rsi = np.where(smma_d == 0, 100,
                       np.where(smma_u == 0, 0,
                                100 * (smma_u / (smma_u + smma_d))))
        return pd.Series(rsi, index=df.index)

    @staticmethod
    def c_chu007(df):
        '''计算vwap因子'''
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        return vwap

    @staticmethod
    def c_chu008(df):
        '''计算vwap背离因子'''
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cumsum() / df['volume'].cumsum()
        vwap_deviation = (df['close'] - vwap) / vwap
        return vwap_deviation

    @staticmethod
    def c_chu009(df):
        '''计算chao1因子'''
        close_diff = df['close'] - df['close'].shift(1)
        volume_diff = df['volume'] - df['volume'].shift(1)
        chao1 = close_diff / volume_diff
        return chao1

    @staticmethod
    def c_chu010(df, window=20):
        '''计算chao3因子'''
        body_ratio = (df['close'] - df['open']) / (df['high'] - df['low'])
        chao3 = body_ratio.ewm(span=window, adjust=False).mean()
        return chao3

    @staticmethod
    def c_chu011(df):
        '''计算chao5因子'''
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        vwap = (typical_price * df['volume']).cumsum() / \
            df['volume'].cumsum()
        chao5 = np.sqrt(df['high'] * df['low']) - vwap
        return chao5

    @staticmethod
    def c_chu012(df, window=10):
        '''计算alpha_010因子'''
        numerator = (df['close'] - df['low']) - (df['high'] - df['close'])
        denominator = df['high'] - df['low']
        alpha_010 = (numerator / denominator *
                     df['volume']).rolling(window).sum()
        return alpha_010

    @staticmethod
    def c_chu013(df, window=6, ma_window=12):
        '''计算alpha_022因子'''
        mean_close_6 = df['close'].rolling(window).mean()
        deviation = (df['close'] - mean_close_6) / mean_close_6
        deviation_change = deviation - deviation.shift(1)
        alpha_022 = deviation_change.ewm(span=ma_window, adjust=False).mean()
        return alpha_022

    @staticmethod
    def c_chu014(df, window=6):
        '''计算alpha_043因子'''
        price_change = df['close'].diff(1)
        volume_contribution = pd.Series(np.where(price_change > 0, df['volume'],
                                                 np.where(price_change < 0, -df['volume'], 0)), index=df.index)
        alpha_043 = volume_contribution.rolling(window).sum()
        return alpha_043

    @staticmethod
    def c_chu015(df, window1=3, window2=6, window3=12, window4=24):
        '''计算alpha_046因子'''
        mean1 = df['close'].rolling(window1).mean()
        mean2 = df['close'].rolling(window2).mean()
        mean3 = df['close'].rolling(window3).mean()
        mean4 = df['close'].rolling(window4).mean()
        alpha_046 = (mean1 + mean2 + mean3 + mean4) / (4 * df['close'])
        return alpha_046

    @staticmethod
    def c_chu016(df, window=12):
        '''计算 alpha_050 因子'''
        high_delay1 = df['high'].shift(1)
        low_delay1 = df['low'].shift(1)

        cond1 = (df['high'] + df['low']) <= (high_delay1 + low_delay1)
        cond2 = (df['high'] + df['low']) >= (high_delay1 + low_delay1)

        delta_high = abs(df['high'] - high_delay1)
        delta_low = abs(df['low'] - low_delay1)

        # 保证数据类型统一
        max_delta = pd.Series(np.maximum(
            delta_high, delta_low), index=df.index)

        S1 = pd.Series(np.where(cond1, 0, max_delta), index=df.index)
        S2 = pd.Series(np.where(cond2, 0, max_delta), index=df.index)

        sum_S1 = S1.rolling(window).sum()
        sum_S2 = S2.rolling(window).sum()

        # 避免除零问题
        eps = 1e-9
        denom = sum_S1 + sum_S2 + eps
        alpha_050 = (sum_S1 / denom) - (sum_S2 / denom)
        return alpha_050

    @staticmethod
    def c_chu017(df, window1=9, window2=3):
        '''计算alpha_057因子'''
        rsv = (df['close'] - df['low'].rolling(window1).min()) / \
            (df['high'].rolling(window1).max() -
             df['low'].rolling(window1).min()) * 100
        alpha_057 = rsv.rolling(window2, min_periods=1).mean()
        return alpha_057

    @staticmethod
    def c_chu018(df, window=20):
        '''计算alpha_076因子'''
        close_delay = df['close'].shift(1)
        abs_return_vol = abs((df['close'] / close_delay - 1)) / df['volume']
        std_20 = abs_return_vol.rolling(window).std()
        mean_20 = abs_return_vol.rolling(window).mean()
        alpha_076 = std_20 / mean_20
        return alpha_076

    @staticmethod
    def c_chu019(df, window=22):
        '''计算alpha_080因子'''
        alpha_080 = df['volume'].ewm(alpha=2 / window, adjust=False).mean()
        return alpha_080

    @staticmethod
    def c_chu020(df, window1=11, window2=4):
        '''计算alpha_111因子'''
        M = df['volume'] * ((df['close'] - df['low']) -
                            (df['high'] - df['close'])) / (df['high'] - df['low'])
        SMA_11 = M.ewm(alpha=2 / window1, adjust=False).mean()
        SMA_4 = M.ewm(alpha=2 / window2, adjust=False).mean()
        alpha_111 = SMA_11 - SMA_4
        return alpha_111

    @staticmethod
    def c_chu021(df, window=12):
        '''计算alpha_127因子'''
        max_close_12 = df['close'].rolling(window).max()
        percent_change_sq = (
            (100 * (df['close'] - max_close_12) / max_close_12) ** 2)
        mean_sq = percent_change_sq.rolling(window).mean()
        alpha_127 = mean_sq.pow(0.5)
        return alpha_127

    @staticmethod
    def c_chu022(df):
        '''计算alpha_150因子'''
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        alpha_150 = typical_price * df['volume']
        return alpha_150

    @staticmethod
    def c_chu023(df, span=13):
        '''计算alpha_173因子'''
        alpha = 2 / (span + 1)
        sma1 = df['close'].ewm(alpha=alpha, adjust=False).mean()
        sma2 = sma1.ewm(alpha=alpha, adjust=False).mean()
        sma3 = np.log(df['close']).ewm(alpha=alpha, adjust=False).mean()
        sma3 = sma3.ewm(alpha=alpha, adjust=False).mean()
        sma3 = sma3.ewm(alpha=alpha, adjust=False).mean()
        alpha_173 = 3 * sma1 - 2 * sma2 + sma3
        return alpha_173

    @staticmethod
    def c_chu024(df, window=6):
        '''计算alpha_175因子'''
        close_delay = df['close'].shift(1)
        high_low = df['high'] - df['low']
        close_high = abs(close_delay - df['high'])
        close_low = abs(close_delay - df['low'])
        max_diff = pd.DataFrame(
            {'high_low': high_low, 'close_high': close_high, 'close_low': close_low}).max(axis=1)
        alpha_175 = max_diff.rolling(window).mean()
        return alpha_175

    @staticmethod
    def c_chu025(df, window=6):
        '''计算alpha_189因子'''
        mean_close_6 = df['close'].rolling(window).mean()
        abs_dev = (df['close'] - mean_close_6).abs()
        alpha_189 = abs_dev.rolling(window).mean()
        return alpha_189

    @staticmethod
    def c_chu026(df, window1=20, window2=10):
        '''计算alpha_191因子'''
        V_mean = df['volume'].rolling(window1).mean()
        Corr = V_mean.rolling(window2).corr(df['close'])
        Mid_Price = (df['high'] + df['low']) / 2
        alpha_191 = Corr + Mid_Price - df['close']
        return alpha_191

    @staticmethod
    def c_chu027(df):
        '''计算ats因子'''
        ats = df['volume'] / df['trade_count']
        return ats

    @staticmethod
    def c_chu028(df, window=20):
        '''计算tcv因子'''
        tcv = df['trade_count'].rolling(window).std()
        return tcv

    @staticmethod
    def c_chu029(df):
        '''计算tvs因子'''
        tvs = df['trade_count'] * df['volume']
        return tvs

    @staticmethod
    def c_chu030(df):
        '''计算k线距离因子'''
        kline_distance = (2*(df['high'] - df['low']) -
                          abs(df['close']-df['open']))/df['volume']
        return kline_distance

    @staticmethod
    def c_chu031(df, window=100):
        '''计算动量因子'''
        log_return = np.log(df['close']/df['close'].shift(1))
        rank = log_return.rolling(window).rank(pct=False)
        momentum = (rank - rank.rolling(100).min()) / (
            rank.rolling(100).max() - rank.rolling(100).min()
        )
        return momentum

    @staticmethod
    def c_chu032(df):
        '''计算upper_shadow1'''
        upper_shadow = df['high']-df[['close', 'open']].max(axis=1)
        return upper_shadow

    @staticmethod
    def c_chu033(df):
        '''计算lower_shadow1'''
        计算lower_shadow = df[['close', 'open']].min(axis=1)-df['low']
        return 计算lower_shadow

    @staticmethod
    def c_chu034(df):
        '''计算upper_shadow2'''
        upper_shadow = df['high']/df[['close', 'open']].max(axis=1)
        return upper_shadow

    @staticmethod
    def c_chu035(df):
        '''计算lower_shadow2'''
        计算lower_shadow = df[['close', 'open']].min(axis=1)/df['low']
        return 计算lower_shadow

    @staticmethod
    def c_chu036(df, window=14):
        '''计算atr因子'''
        tr1 = df['high'] - df['low']
        tr2 = abs(df['high'] - df['close'].shift(1))
        tr3 = abs(df['close'].shift(1) - df['low'])
        tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)
        atr = tr.ewm(alpha=1 / window, adjust=False).mean()

        return atr/df['close']

    @staticmethod
    def c_chu037(df):
        '''计算c_chu009因子的abs'''
        close_diff = df['close'] - df['close'].shift(1)
        volume_diff = df['volume'] - df['volume'].shift(1)
        chao1 = close_diff / volume_diff
        return abs(chao1)

    @staticmethod
    def c_chu038(df, window=20):
        '''计算c_chu010因子的abs'''
        body_ratio = (df['close'] - df['open']) / (df['high'] - df['low'])
        chao3 = body_ratio.ewm(span=window, adjust=False).mean()*df['volume']
        return abs(chao3)

    @staticmethod
    def c_chu039(df, window=10):
        '''计算c_chu012因子的abs'''
        numerator = (df['close'] - df['low']) - (df['high'] - df['close'])
        denominator = df['high'] - df['low']
        alpha_010 = (numerator / denominator *
                     df['volume']).rolling(window).sum()
        return abs(alpha_010)

    @staticmethod
    def c_chu040(df, window=6, ma_window=12):
        '''计算c_chu013因子的abs'''
        mean_close_6 = df['close'].rolling(window).mean()
        deviation = (df['close'] - mean_close_6) / mean_close_6
        deviation_change = deviation - deviation.shift(1)
        alpha_022 = deviation_change.ewm(span=ma_window, adjust=False).mean()
        return abs(alpha_022)

    @staticmethod
    def c_chu041(df, window=6):
        '''计算c_chu014因子的abs'''
        price_change = df['close'].diff(1)
        volume_contribution = pd.Series(np.where(price_change > 0, df['volume'],
                                                 np.where(price_change < 0, -df['volume'], 0)), index=df.index)
        alpha_043 = volume_contribution.rolling(window).sum()
        return abs(alpha_043)

    @staticmethod
    def c_chu042(df, window1=3, window2=6, window3=12, window4=24):
        '''计算c_chu015因子-1的abs'''
        mean1 = df['close'].rolling(window1).mean()
        mean2 = df['close'].rolling(window2).mean()
        mean3 = df['close'].rolling(window3).mean()
        mean4 = df['close'].rolling(window4).mean()
        alpha_046 = (mean1 + mean2 + mean3 + mean4) / (4 * df['close'])
        return abs(alpha_046-1)

    @staticmethod
    def c_chu043(df, window1=11, window2=4):
        '''计算c_chu020因子的abs'''
        M = df['volume'] * ((df['close'] - df['low']) -
                            (df['high'] - df['close'])) / (df['high'] - df['low'])
        SMA_11 = M.ewm(alpha=2 / window1, adjust=False).mean()
        SMA_4 = M.ewm(alpha=2 / window2, adjust=False).mean()
        alpha_111 = SMA_11 - SMA_4
        return abs(alpha_111)

    @staticmethod
    def c_chu044(df, window=20):
        '''计算c_chu010因子的平方'''
        body_ratio = (df['close'] - df['open']) / (df['high'] - df['low'])
        chao3 = body_ratio.ewm(span=window, adjust=False).mean()*df['volume']
        return chao3**2

    @staticmethod
    def c_chu045(df, window=10):
        '''计算c_chu012因子的平方'''
        numerator = (df['close'] - df['low']) - (df['high'] - df['close'])
        denominator = df['high'] - df['low']
        alpha_010 = (numerator / denominator *
                     df['volume']).rolling(window).sum()
        return alpha_010**2

    @staticmethod
    def c_chu046(df, window=6, ma_window=12):
        '''计算c_chu013因子的平方'''
        mean_close_6 = df['close'].rolling(window).mean()
        deviation = (df['close'] - mean_close_6) / mean_close_6
        deviation_change = deviation - deviation.shift(1)
        alpha_022 = deviation_change.ewm(span=ma_window, adjust=False).mean()
        return alpha_022**2

    @staticmethod
    def c_chu047(df, window=6):
        '''计算c_chu014因子的平方'''
        price_change = df['close'].diff(1)
        volume_contribution = pd.Series(np.where(price_change > 0, df['volume'],
                                                 np.where(price_change < 0, -df['volume'], 0)), index=df.index)
        alpha_043 = volume_contribution.rolling(window).sum()
        return abs(alpha_043)

    @staticmethod
    def c_chu048(df, window1=3, window2=6, window3=12, window4=24):
        '''计算c_chu015因子-1的平方'''
        mean1 = df['close'].rolling(window1).mean()
        mean2 = df['close'].rolling(window2).mean()
        mean3 = df['close'].rolling(window3).mean()
        mean4 = df['close'].rolling(window4).mean()
        alpha_046 = (mean1 + mean2 + mean3 + mean4) / (4 * df['close'])
        return (alpha_046-1)**2

    @staticmethod
    def c_chu049(df, window1=11, window2=4):
        '''计算c_chu020因子的平方'''
        M = df['volume'] * ((df['close'] - df['low']) -
                            (df['high'] - df['close'])) / (df['high'] - df['low'])
        SMA_11 = M.ewm(alpha=2 / window1, adjust=False).mean()
        SMA_4 = M.ewm(alpha=2 / window2, adjust=False).mean()
        alpha_111 = SMA_11 - SMA_4
        return alpha_111**2

    @staticmethod
    def c_chu050(df, window1=14):
        # 计算 MAX_HIGH 和 MAX_LOW
        max_high = (df['high'] > df['high'].shift(1)).astype(
            int) * (df['high'] - df['high'].shift(1))
        max_low = (df['low'].shift(1) > df['low']).astype(
            int) * (df['low'].shift(1) - df['low'])

        # 计算 XPDM 和 XNDM
        xpdm = (max_high > max_low).astype(int) * \
            (df['high'] - df['high'].shift(1))
        xndm = (max_low > max_high).astype(int) * \
            (df['low'].shift(1) - df['low'])

        # 计算 PDM 和 NDM
        pdm = xpdm.rolling(window1).sum()
        ndm = xndm.rolling(window1).sum()

        # 计算 True Range (TR)
        tr = pd.DataFrame({
            'high_low': (df['high'] - df['low']).abs(),
            'high_close': (df['high'] - df['close'].shift(1)).abs(),
            'low_close': (df['low'] - df['close'].shift(1)).abs()
        })
        tr = tr.max(axis=1).rolling(window1).sum()

        # 计算 DI+ 和 DI-
        di_plus = pdm / tr * 100
        di_minus = ndm / tr * 100

        return di_plus+di_minus

    @staticmethod
    def c_chu051(df, N1=10, N2=40, N3=20):
        """
        计算 ROC, ROC_MA, PMO 和 PMO_SIGNAL 指标

        参数:
        df : pd.DataFrame，包含 'close' 列
        N1 : int，ROC_MA 的平滑周期（默认10）
        N2 : int，PMO 的平滑周期（默认40）
        N3 : int，PMO_SIGNAL 的平滑周期（默认20）

        返回:
        pd.DataFrame，包含 ROC, ROC_MA, PMO, PMO_SIGNAL 列
        """
        # 计算 ROC = (CLOSE - REF(CLOSE,1)) / REF(CLOSE,1) * 100
        ROC = (df['close'] - df['close'].shift(1)) / df['close'].shift(1) * 100

        # 计算 ROC_MA = DMA(ROC, 2/(N1+1)) （动态移动平均）
        alpha_roc = 2 / (N1 + 1)
        ROC_MA = ROC.ewm(alpha=alpha_roc, adjust=False).mean()

        return abs(ROC_MA)

    @staticmethod
    def c_chu052(df, N1=34, N2=55):
        """
        计算 Klinger Oscillator (克林格振荡器)

        参数:
        df : pd.DataFrame，包含 'high', 'low', 'close', 'volume' 列
        N1 : int，短期EMA周期（默认34）
        N2 : int，长期EMA周期（默认55）

        返回:
        pd.Series，包含 KO 值
        """
        # 计算典型价格
        TYPICAL = (df['high'] + df['low'] + df['close']) / 3

        # 计算方向性成交量
        VOLUME = np.where(
            TYPICAL >= TYPICAL.shift(1),
            df['volume'],
            -df['volume']
        )

        # 计算EMA
        VOLUME_EMA1 = pd.Series(VOLUME).ewm(span=N1, adjust=False).mean()
        VOLUME_EMA2 = pd.Series(VOLUME).ewm(span=N2, adjust=False).mean()

        # 计算KO线
        KO = pd.Series(VOLUME_EMA1 - VOLUME_EMA2)
        KO.index = df.index

        return abs(KO)

    @fill_window(224)
    @staticmethod
    def d_ret_hv_ratio_signals_he1(d, short_period=10, long_period=30, threshold_high=1.5, threshold_low=0.5):
        c = d.close

        # 计算每日收益率的对数
        log_returns = np.log(c / c.shift(1))

        # 计算短期HV
        hv_short = log_returns.rolling(
            window=short_period).std() * np.sqrt(252)  # 年化波动率

        # 计算长期HV
        hv_long = log_returns.rolling(
            window=long_period).std() * np.sqrt(252)  # 年化波动率

        # 计算HV比率
        hv_ratio = hv_short / hv_long

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(len(hv_ratio)):
            # HV比率低于低阈值时，产生买入信号
            if hv_ratio[i] < threshold_low:
                buy_signal[i] = 1  # 买入信号

            # HV比率高于高阈值时，产生卖出信号
            elif hv_ratio[i] > threshold_high:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        short_window = 12
        long_window = 26
        v = d.volume

        short_ema = v.ewm(span=short_window, adjust=False).mean()
        long_ema = v.ewm(span=long_window, adjust=False).mean()
        f = (short_ema - long_ema) / long_ema
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(40)
    @staticmethod
    def d_ret_hv_ratio_signals_gong1(d, short_period=10, long_period=30, threshold_high=1.5, threshold_low=0.5):
        c = d.close

        # 计算每日收益率的对数
        log_returns = np.log(c / c.shift(1))

        # 计算短期HV
        hv_short = log_returns.rolling(
            window=short_period).std() * np.sqrt(252)  # 年化波动率

        # 计算长期HV
        hv_long = log_returns.rolling(
            window=long_period).std() * np.sqrt(252)  # 年化波动率

        # 计算HV比率
        hv_ratio = hv_short / hv_long

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(len(hv_ratio)):
            # HV比率低于低阈值时，产生买入信号
            if hv_ratio[i] < threshold_low:
                buy_signal[i] = 1  # 买入信号

            # HV比率高于高阈值时，产生卖出信号
            elif hv_ratio[i] > threshold_high:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        # 计算对数收益率
        log_returns = np.log(d['close'] / d['close'].shift(1))

        # 计算滚动标准差
        volatility = log_returns.rolling(window=60).std() * np.sqrt(1440)

        v_short = volatility.rolling(window=10).mean()
        v_long = volatility.rolling(window=300).mean()

        f1 = v_short / v_long
        s[f1 < 1.5] = 0
        ######################################################
        v = d.volume

        ma_short = v.rolling(window=10).mean()
        ma_long = v.rolling(window=300).mean()

        f2 = ma_short / ma_long
        s[f2 > 0.9] = 0

        # window = 40

        return s

    @fill_window(60)
    @staticmethod
    def d_ret_td_signals_ge1(d):
        c = d.close
        td_buy_count = 0
        td_sell_count = 0
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # TD 买入结构
        for i in range(4, len(c)):
            # 买入结构
            if c[i] < c[i - 4]:  # 当前收盘价低于4天前收盘价
                td_buy_count += 1
            else:
                td_buy_count = 0  # 重置计数

            # 满足连续9天买入条件
            if td_buy_count == 9:
                buy_signal[i] = 1  # 产生买入信号
                td_buy_count = 0  # 重置计数器

            # 卖出结构
            if c[i] > c[i - 4]:  # 当前收盘价高于4天前收盘价
                td_sell_count += 1
            else:
                td_sell_count = 0  # 重置计数

            # 满足连续9天卖出条件
            if td_sell_count == 9:
                sell_signal[i] = -1  # 产生卖出信号
                td_sell_count = 0  # 重置计数器

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s

    @fill_window(84)
    @staticmethod
    def d_ret_td_signals_he1(d):
        c = d.close
        td_buy_count = 0
        td_sell_count = 0
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # TD 买入结构
        for i in range(4, len(c)):
            # 买入结构
            if c[i] < c[i - 4]:  # 当前收盘价低于4天前收盘价
                td_buy_count += 1
            else:
                td_buy_count = 0  # 重置计数

            # 满足连续9天买入条件
            if td_buy_count == 9:
                buy_signal[i] = 1  # 产生买入信号
                td_buy_count = 0  # 重置计数器

            # 卖出结构
            if c[i] > c[i - 4]:  # 当前收盘价高于4天前收盘价
                td_sell_count += 1
            else:
                td_sell_count = 0  # 重置计数

            # 满足连续9天卖出条件
            if td_sell_count == 9:
                sell_signal[i] = -1  # 产生卖出信号
                td_sell_count = 0  # 重置计数器

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s

    @fill_window(170)
    @staticmethod
    def d_ret_ao_signals_he1(d):
        c = d.close

        # 计算AO指标
        fast_ma = c.rolling(window=5).mean()  # 快速移动平均线（5周期）
        slow_ma = c.rolling(window=34).mean()  # 慢速移动平均线（34周期）
        ao = fast_ma - slow_ma  # AO = 快速MA - 慢速MA

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # AO由负变正产生买入信号
        for i in range(1, len(ao)):
            if ao[i - 1] < 0 and ao[i] > 0:  # AO由负变正
                buy_signal[i] = 1  # 产生买入信号

            # AO由正变负产生卖出信号
            elif ao[i - 1] > 0 and ao[i] < 0:  # AO由正变负
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(60)
    @staticmethod
    def d_ret_ao_signals_gong1(d):
        c = d.close

        # 计算AO指标
        fast_ma = c.rolling(window=5).mean()  # 快速移动平均线（5周期）
        slow_ma = c.rolling(window=34).mean()  # 慢速移动平均线（34周期）
        ao = fast_ma - slow_ma  # AO = 快速MA - 慢速MA

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # AO由负变正产生买入信号
        for i in range(1, len(ao)):
            if ao[i - 1] < 0 and ao[i] > 0:  # AO由负变正
                buy_signal[i] = 1  # 产生买入信号

            # AO由正变负产生卖出信号
            elif ao[i - 1] > 0 and ao[i] < 0:  # AO由正变负
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        c = d.close

        high = c.rolling(window=60).max()
        low = c.rolling(window=60).min()
        mid = (high + low) / 2

        f1 = c / mid
        s[f1 < 1.001] = 0
        ######################################################
        c = d.close

        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f2 = ma_short / ma_long
        s[f2 > 1.02] = 0

        return s

    @fill_window(134)
    @staticmethod
    def d_ret_ena_signals_he1(d):
        c = d.close
    # 计算指数加权移动平均（EMA）
        ema_period = 14
        ema = c.ewm(span=ema_period, adjust=False).mean()

        # 归一化ENA指标
        max_ema = ema.max()  # 计算EMA的最大值
        min_ema = ema.min()  # 计算EMA的最小值
        ena = (ema - min_ema) / (max_ema - min_ema)  # 归一化ENA

    # 计算ENA的信号线（EMA的移动平均）
        ena_signal = ena.ewm(span=9, adjust=False).mean()  # 9周期的EMA作为信号线

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # ENA由下向上穿越信号线时产生买入信号
        for i in range(1, len(ena)):
            if ena[i - 1] < ena_signal[i - 1] and ena[i] > ena_signal[i]:  # ENA由下向上穿越信号线
                buy_signal[i] = 1  # 产生买入信号

            # ENA由上向下穿越信号线时产生卖出信号
            elif ena[i - 1] > ena_signal[i - 1] and ena[i] < ena_signal[i]:  # ENA由上向下穿越信号线
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(94)
    @staticmethod
    def d_ret_williams_r_sig_price_he1(d, period=14):
        c = d.close  # 获取收盘价

        # 计算威廉指标（Williams %R）
        highest_high = d.high.rolling(window=period).max()  # 过去14天的最高价
        lowest_low = d.low.rolling(window=period).min()     # 过去14天的最低价
        williams_r = -100 * (highest_high - c) / \
            (highest_high - lowest_low)  # Williams %R 计算

        # 买入信号：威廉指标低于-80
        buy_signal = williams_r < -80

        # 卖出信号：威廉指标高于-20
        sell_signal = williams_r > -20

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal.astype(int) - sell_signal.astype(int)

        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s  # 返回信号序列

    @fill_window(1)
    @staticmethod
    def d_ret_williams_r_sig_price_gong1(d, period=14):
        c = d.close  # 获取收盘价

        # 计算威廉指标（Williams %R）
        highest_high = d.high.rolling(window=period).max()  # 过去14天的最高价
        lowest_low = d.low.rolling(window=period).min()     # 过去14天的最低价
        williams_r = -100 * (highest_high - c) / \
            (highest_high - lowest_low)  # Williams %R 计算

        # 买入信号：威廉指标低于-80
        buy_signal = williams_r < -80

        # 卖出信号：威廉指标高于-20
        sell_signal = williams_r > -20

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal.astype(int) - sell_signal.astype(int)

        c = d.close

        high = c.rolling(window=60).max()
        low = c.rolling(window=60).min()
        mid = (high + low) / 2

        f1 = c / mid
        s[f1 > 0.98] = 0

        return s  # 返回信号序列

    @fill_window(1)
    @staticmethod
    def d_ret_momentum_sig_price_ge1(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

    # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

    # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s  # 返回信号序列

    @fill_window(50)
    @staticmethod
    def d_ret_momentum_sig_price_ge2(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

    # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

    # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s  # 返回信号序列

    @fill_window(1)
    @staticmethod
    def d_ret_momentum_sig_price_ge3(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

    # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

    # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s  # 返回信号序列

    @fill_window(50)
    @staticmethod
    def d_ret_momentum_sig_price_ge4(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

    # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

    # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s  # 返回信号序列

    @fill_window(90)
    @staticmethod
    def d_ret_momentum_sig_price_ge5(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

    # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

    # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        c = d.close

        window = 240
        # 计算价格的均值和标准差
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s  # 返回信号序列

    @fill_window(1)
    @staticmethod
    def d_ret_momentum_sig_price_ge6(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

    # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

    # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        c = d.close

        window = 240
        # 计算价格的均值和标准差
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s  # 返回信号序列

    @fill_window(10)
    @staticmethod
    def d_ret_momentum_sig_price_ge7(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

    # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

    # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        c = d.close

        window = 240
        # 计算价格的均值和标准差
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s  # 返回信号序列

    @fill_window(1)
    @staticmethod
    def d_ret_momentum_sig_price_he1(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

    # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

    # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s  # 返回信号序列

    @fill_window(100)
    @staticmethod
    def d_ret_momentum_sig_price_he2(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

    # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

    # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s  # 返回信号序列

    @fill_window(1)
    @staticmethod
    def d_ret_kc_strategy_he1(d):
        c = d.close
        high = d.high
        low = d.low

        # 设置参数
        kc_period = 20  # 均线计算周期
        atr_period = 14  # ATR计算周期
        atr_multiplier = 2  # ATR倍数

        # 计算20日均线
        ma = c.rolling(window=kc_period).mean()

        # 计算ATR
        tr1 = high - low
        tr2 = (high - c.shift(1)).abs()
        tr3 = (low - c.shift(1)).abs()
        true_range = tr1.combine(tr2, max).combine(tr3, max)
        atr = true_range.rolling(window=atr_period).mean()

        # 计算Keltner Channel上下轨
        kc_upper = ma + (atr * atr_multiplier)  # KC上轨
        kc_lower = ma - (atr * atr_multiplier)  # KC下轨

        # 生成买入和卖出信号
        buy_signal = (c < kc_lower).astype(int)  # 当价格跌破KC下轨时买入
        sell_signal = (c > kc_upper).astype(int)  # 当价格突破KC上轨时卖出

    # 合并信号：1为买入，-1为卖出，0为中性
        s = buy_signal - sell_signal

        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(84)
    @staticmethod
    def d_ret_kc_strategy_he2(d):
        c = d.close
        high = d.high
        low = d.low

        # 设置参数
        kc_period = 20  # 均线计算周期
        atr_period = 14  # ATR计算周期
        atr_multiplier = 2  # ATR倍数

        # 计算20日均线
        ma = c.rolling(window=kc_period).mean()

        # 计算ATR
        tr1 = high - low
        tr2 = (high - c.shift(1)).abs()
        tr3 = (low - c.shift(1)).abs()
        true_range = tr1.combine(tr2, max).combine(tr3, max)
        atr = true_range.rolling(window=atr_period).mean()

        # 计算Keltner Channel上下轨
        kc_upper = ma + (atr * atr_multiplier)  # KC上轨
        kc_lower = ma - (atr * atr_multiplier)  # KC下轨

        # 生成买入和卖出信号
        buy_signal = (c < kc_lower).astype(int)  # 当价格跌破KC下轨时买入
        sell_signal = (c > kc_upper).astype(int)  # 当价格突破KC上轨时卖出

    # 合并信号：1为买入，-1为卖出，0为中性
        s = buy_signal - sell_signal

        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_kc_strategy_gong1(d):
        c = d.close
        high = d.high
        low = d.low

        # 设置参数
        kc_period = 20  # 均线计算周期
        atr_period = 14  # ATR计算周期
        atr_multiplier = 2  # ATR倍数

        # 计算20日均线
        ma = c.rolling(window=kc_period).mean()

        # 计算ATR
        tr1 = high - low
        tr2 = (high - c.shift(1)).abs()
        tr3 = (low - c.shift(1)).abs()
        true_range = tr1.combine(tr2, max).combine(tr3, max)
        atr = true_range.rolling(window=atr_period).mean()

        # 计算Keltner Channel上下轨
        kc_upper = ma + (atr * atr_multiplier)  # KC上轨
        kc_lower = ma - (atr * atr_multiplier)  # KC下轨

        # 生成买入和卖出信号
        buy_signal = (c < kc_lower).astype(int)  # 当价格跌破KC下轨时买入
        sell_signal = (c > kc_upper).astype(int)  # 当价格突破KC上轨时卖出

    # 合并信号：1为买入，-1为卖出，0为中性
        s = buy_signal - sell_signal
        c = d.close

        high = c.rolling(window=60).max()
        low = c.rolling(window=60).min()
        mid = (high + low) / 2

        f1 = c / mid
        s[f1 > 0.985] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_bollinger_rsi_signals_ge1(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # 布林带和 RSI 的标准计算周期
        rsi_period = 14  # RSI 的计算周期

        # 计算 RSI
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 计算布林带
        rolling_mean = c.rolling(window=period).mean()
        rolling_std = c.rolling(window=period).std()
        lower_band = rolling_mean - (rolling_std * 2)  # 下轨
        upper_band = rolling_mean + (rolling_std * 2)  # 上轨

    # 买入信号：价格触及布林带下轨且 RSI 低于 30（超卖）
        buy_signal = ((c <= lower_band) & (rsi < 30)).astype(int)

    # 卖出信号：价格触及布林带上轨且 RSI 高于 70（超买）
        sell_signal = ((c >= upper_band) & (rsi > 70)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal - sell_signal

        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(105)
    @staticmethod
    def d_ret_bollinger_rsi_signals_ge2(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # 布林带和 RSI 的标准计算周期
        rsi_period = 14  # RSI 的计算周期

        # 计算 RSI
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 计算布林带
        rolling_mean = c.rolling(window=period).mean()
        rolling_std = c.rolling(window=period).std()
        lower_band = rolling_mean - (rolling_std * 2)  # 下轨
        upper_band = rolling_mean + (rolling_std * 2)  # 上轨

    # 买入信号：价格触及布林带下轨且 RSI 低于 30（超卖）
        buy_signal = ((c <= lower_band) & (rsi < 30)).astype(int)

    # 卖出信号：价格触及布林带上轨且 RSI 高于 70（超买）
        sell_signal = ((c >= upper_band) & (rsi > 70)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal - sell_signal

        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_bollinger_rsi_signals_he1(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # 布林带和 RSI 的标准计算周期
        rsi_period = 14  # RSI 的计算周期

        # 计算 RSI
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 计算布林带
        rolling_mean = c.rolling(window=period).mean()
        rolling_std = c.rolling(window=period).std()
        lower_band = rolling_mean - (rolling_std * 2)  # 下轨
        upper_band = rolling_mean + (rolling_std * 2)  # 上轨

    # 买入信号：价格触及布林带下轨且 RSI 低于 30（超卖）
        buy_signal = ((c <= lower_band) & (rsi < 30)).astype(int)

    # 卖出信号：价格触及布林带上轨且 RSI 高于 70（超买）
        sell_signal = ((c >= upper_band) & (rsi > 70)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal - sell_signal
        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(90)
    @staticmethod
    def d_ret_bollinger_rsi_signals_he2(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # 布林带和 RSI 的标准计算周期
        rsi_period = 14  # RSI 的计算周期

        # 计算 RSI
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 计算布林带
        rolling_mean = c.rolling(window=period).mean()
        rolling_std = c.rolling(window=period).std()
        lower_band = rolling_mean - (rolling_std * 2)  # 下轨
        upper_band = rolling_mean + (rolling_std * 2)  # 上轨

    # 买入信号：价格触及布林带下轨且 RSI 低于 30（超卖）
        buy_signal = ((c <= lower_band) & (rsi < 30)).astype(int)

    # 卖出信号：价格触及布林带上轨且 RSI 高于 70（超买）
        sell_signal = ((c >= upper_band) & (rsi > 70)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal - sell_signal
        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(81)
    @staticmethod
    def d_ret_macd_sig_price_he1(d):
        c = d.close
        period_short = 12  # 快速EMA的周期
        period_long = 26   # 慢速EMA的周期
        signal_period = 9  # 信号线的周期

        # 计算快速和慢速EMA
        ema_short = c.ewm(span=period_short, adjust=False).mean()
        ema_long = c.ewm(span=period_long, adjust=False).mean()

        # 计算 MACD 线
        macd_line = ema_short - ema_long

        # 计算信号线
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()

    # 买入信号：MACD 线向上穿越信号线
        golden_cross_buy = ((macd_line.shift(1) < signal_line.shift(1)) & (
            macd_line >= signal_line)).astype(int)

    # 卖出信号：MACD 线向下穿越信号线
        death_cross_sell = ((macd_line.shift(1) > signal_line.shift(1)) & (
            macd_line <= signal_line)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        c = d.close
        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ###############################################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s

    @fill_window(80)
    @staticmethod
    def d_ret_macd_sig_price_gong1(d):
        c = d.close
        period_short = 12  # 快速EMA的周期
        period_long = 26   # 慢速EMA的周期
        signal_period = 9  # 信号线的周期

        # 计算快速和慢速EMA
        ema_short = c.ewm(span=period_short, adjust=False).mean()
        ema_long = c.ewm(span=period_long, adjust=False).mean()

        # 计算 MACD 线
        macd_line = ema_short - ema_long

        # 计算信号线
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()

    # 买入信号：MACD 线向上穿越信号线
        golden_cross_buy = ((macd_line.shift(1) < signal_line.shift(1)) & (
            macd_line >= signal_line)).astype(int)

    # 卖出信号：MACD 线向下穿越信号线
        death_cross_sell = ((macd_line.shift(1) > signal_line.shift(1)) & (
            macd_line <= signal_line)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        log_returns = np.log(d['close'] / d['close'].shift(1))

    # 计算滚动标准差
        volatility = log_returns.rolling(window=60).std() * np.sqrt(1440)

        v_short = volatility.rolling(window=10).mean()
        v_long = volatility.rolling(window=300).mean()

        f1 = v_short / v_long
        s[f1 > 1.02] = 0
        ######################################################
        c = d.close

        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f2 = ma_short / ma_long
        s[f2 > 0.985] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_ma_arrangement_sig_ge1(d):
        close = d.close  # 收盘价
        low = d.low      # K线最低价

        # 计算均线
        ma5 = close.rolling(window=5).mean()
        ma10 = close.rolling(window=10).mean()
        ma20 = close.rolling(window=20).mean()

        # 买入信号：第一次出现币价 > MA5 > MA10 > MA20
        buy_signal = ((low > ma5) & (ma5 > ma10) & (ma10 > ma20) &
                      (~((low.shift(1) > ma5.shift(1)) & (ma5.shift(1) > ma10.shift(1)) & (ma10.shift(1) > ma20.shift(1))))).astype(int)

        # 卖出信号：第一次出现币价 < MA5 < MA10 < MA20
        sell_signal = ((low < ma5) & (ma5 < ma10) & (ma10 < ma20) &
                       (~((low.shift(1) < ma5.shift(1)) & (ma5.shift(1) < ma10.shift(1)) & (ma10.shift(1) < ma20.shift(1))))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal - sell_signal
        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################
        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0
        return s

    @fill_window(10)
    @staticmethod
    def d_ret_ma_arrangement_sig_ge2(d):
        close = d.close  # 收盘价
        low = d.low      # K线最低价

        # 计算均线
        ma5 = close.rolling(window=5).mean()
        ma10 = close.rolling(window=10).mean()
        ma20 = close.rolling(window=20).mean()

        # 买入信号：第一次出现币价 > MA5 > MA10 > MA20
        buy_signal = ((low > ma5) & (ma5 > ma10) & (ma10 > ma20) &
                      (~((low.shift(1) > ma5.shift(1)) & (ma5.shift(1) > ma10.shift(1)) & (ma10.shift(1) > ma20.shift(1))))).astype(int)

        # 卖出信号：第一次出现币价 < MA5 < MA10 < MA20
        sell_signal = ((low < ma5) & (ma5 < ma10) & (ma10 < ma20) &
                       (~((low.shift(1) < ma5.shift(1)) & (ma5.shift(1) < ma10.shift(1)) & (ma10.shift(1) < ma20.shift(1))))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal - sell_signal

        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s

    @fill_window(20)
    @staticmethod
    def d_ret_ma_arrangement_sig_ge3(d):
        close = d.close  # 收盘价
        low = d.low      # K线最低价

        # 计算均线
        ma5 = close.rolling(window=5).mean()
        ma10 = close.rolling(window=10).mean()
        ma20 = close.rolling(window=20).mean()

        # 买入信号：第一次出现币价 > MA5 > MA10 > MA20
        buy_signal = ((low > ma5) & (ma5 > ma10) & (ma10 > ma20) &
                      (~((low.shift(1) > ma5.shift(1)) & (ma5.shift(1) > ma10.shift(1)) & (ma10.shift(1) > ma20.shift(1))))).astype(int)

        # 卖出信号：第一次出现币价 < MA5 < MA10 < MA20
        sell_signal = ((low < ma5) & (ma5 < ma10) & (ma10 < ma20) &
                       (~((low.shift(1) < ma5.shift(1)) & (ma5.shift(1) < ma10.shift(1)) & (ma10.shift(1) < ma20.shift(1))))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal - sell_signal

        return s

    @fill_window(106)
    @staticmethod
    def d_ret_ma_arrangement_sig_he1(d):
        close = d.close  # 收盘价
        low = d.low      # K线最低价

        # 计算均线
        ma5 = close.rolling(window=5).mean()
        ma10 = close.rolling(window=10).mean()
        ma20 = close.rolling(window=20).mean()

        # 买入信号：第一次出现币价 > MA5 > MA10 > MA20
        buy_signal = ((low > ma5) & (ma5 > ma10) & (ma10 > ma20) &
                      (~((low.shift(1) > ma5.shift(1)) & (ma5.shift(1) > ma10.shift(1)) & (ma10.shift(1) > ma20.shift(1))))).astype(int)

        # 卖出信号：第一次出现币价 < MA5 < MA10 < MA20
        sell_signal = ((low < ma5) & (ma5 < ma10) & (ma10 < ma20) &
                       (~((low.shift(1) < ma5.shift(1)) & (ma5.shift(1) < ma10.shift(1)) & (ma10.shift(1) < ma20.shift(1))))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal - sell_signal
        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_ma20_ma120_cross_sig_price_he1(d):

        c = d.close

        # 计算 MA20 和 MA120
        ma20 = c.rolling(window=20).mean()
        ma120 = c.rolling(window=120).mean()

        # 创建买入信号
        condu = ((c > ma120) & (c > ma20) & (c.shift(1) > ma120)
                 & (c.shift(1) <= ma20)).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (c < ma20) & (c.shift(1) < ma120)
                 & (c.shift(1) >= ma20)).astype(int)

        s = condu - condd
        c = d.close
        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(80)
    @staticmethod
    def d_ret_ma20_ma120_cross_sig_price_he2(d):

        c = d.close

        # 计算 MA20 和 MA120
        ma20 = c.rolling(window=20).mean()
        ma120 = c.rolling(window=120).mean()

        # 创建买入信号
        condu = ((c > ma120) & (c > ma20) & (c.shift(1) > ma120)
                 & (c.shift(1) <= ma20)).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (c < ma20) & (c.shift(1) < ma120)
                 & (c.shift(1) >= ma20)).astype(int)

        s = condu - condd
        c = d.close
        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(80)
    @staticmethod
    def d_ret_ma20_ma120_cross_sig_price_gong1(d):

        c = d.close

        # 计算 MA20 和 MA120
        ma20 = c.rolling(window=20).mean()
        ma120 = c.rolling(window=120).mean()

        # 创建买入信号
        condu = ((c > ma120) & (c > ma20) & (c.shift(1) > ma120)
                 & (c.shift(1) <= ma20)).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (c < ma20) & (c.shift(1) < ma120)
                 & (c.shift(1) >= ma20)).astype(int)

        s = condu - condd

        price_ratio = d['close'] / d['open']

        is_bullish = price_ratio > 1.0001  # 阳线
        is_bearish = price_ratio < 0.9999  # 阴线

        bullish_count = is_bullish.rolling(window=60).sum()
        bearish_count = is_bearish.rolling(window=60).sum()

        f1 = bullish_count / (bullish_count + bearish_count)
        s[f1 < 0.55] = 0

        return s

    @fill_window(73)
    @staticmethod
    def d_ret_ma120_macd_1_cross_sig_price_he1(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算MACD
        def compute_macd(c, short_window=12, long_window=26, signal_window=9):
            exp1 = c.ewm(span=short_window, adjust=False).mean()
            exp2 = c.ewm(span=long_window, adjust=False).mean()
            macd = exp1 - exp2
            signal = macd.ewm(span=signal_window, adjust=False).mean()
            macd_hist = macd - signal
            return macd_hist

        hist = compute_macd(c)
        # 创建买入信号
        condu = ((c > ma120) & (hist.shift(1) < 0) & (hist > 0)).astype(int)
        # 创建卖出信号
        condd = ((c < ma120) & (hist.shift(1) > 0) & (hist < 0)).astype(int)

        s = condu - condd
        c = d.close
        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(15)
    @staticmethod
    def d_ret_ma120_macd_1_cross_sig_price_gong1(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算MACD
        def compute_macd(c, short_window=12, long_window=26, signal_window=9):
            exp1 = c.ewm(span=short_window, adjust=False).mean()
            exp2 = c.ewm(span=long_window, adjust=False).mean()
            macd = exp1 - exp2
            signal = macd.ewm(span=signal_window, adjust=False).mean()
            macd_hist = macd - signal
            return macd_hist

        hist = compute_macd(c)
        # 创建买入信号
        condu = ((c > ma120) & (hist.shift(1) < 0) & (hist > 0)).astype(int)
        # 创建卖出信号
        condd = ((c < ma120) & (hist.shift(1) > 0) & (hist < 0)).astype(int)

        s = condu - condd

        c = d.close

        high = c.rolling(window=60).max()
        low = c.rolling(window=60).min()
        mid = (high + low) / 2

        f1 = c / mid
        s[f1 < 1.01] = 0

        return s

    @fill_window(85)
    @staticmethod
    def d_ret_ma120_bolling_cross_sig_price_ge1(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                                                    lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                                                    upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd

        c = d.close

        long_T = 30
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s

    @fill_window(30)
    @staticmethod
    def d_ret_ma120_bolling_cross_sig_price_ge2(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                                                    lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                                                    upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd

        c = d.close

        long_T = 30
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_ma120_bolling_cross_sig_price_ge3(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                                                    lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                                                    upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd
        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_ma120_bolling_cross_sig_price_ge4(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                                                    lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                                                    upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd

        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s

    @fill_window(80)
    @staticmethod
    def d_ret_ma120_bolling_cross_sig_price_ge5(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                                                    lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                                                    upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd

        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s

    @fill_window(77)
    @staticmethod
    def d_ret_ma120_bolling_cross_sig_price_he1(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                                                    lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                                                    upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd

        delta = d.close.diff()
        periods = 14
        gain = (delta.where(delta > 0, 0)).rolling(window=periods).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=periods).mean()
        rs_rate = gain / loss
        f = 100 - (100 / (1 + rs_rate))
        ###############################################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s

    @fill_window(1000)
    @staticmethod
    def d_ret_ma120_cci_cross_sig_price_he1(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 CCI
        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        cci_20 = compute_rsi(d, 20)

        # 创建买入信号
        condu = ((c > ma120) & (cci_20 < -100) & (c > c.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (cci_20 > 100) & (c < c.shift(1))).astype(int)

        s = condu - condd

        return s

    @fill_window(260)
    @staticmethod
    def d_ret_ma120_cci_cross_sig_price_gong1(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 CCI
        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        cci_20 = compute_rsi(d, 20)

        # 创建买入信号
        condu = ((c > ma120) & (cci_20 < -100) & (c > c.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (cci_20 > 100) & (c < c.shift(1))).astype(int)

        s = condu - condd
        c = d.close

        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f1 = ma_short / ma_long
        s[f1 < 1.02] = 0
        return s

    @fill_window(60)
    @staticmethod
    def d_ret_macd_02_cross_sig_price_ge1(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd
        c = d.close

        long_T = 30
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_macd_02_cross_sig_price_ge2(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd
        c = d.close

        long_T = 30
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(10)
    @staticmethod
    def d_ret_macd_02_cross_sig_price_ge3(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd

        c = d.close

        long_T = 30
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(60)
    @staticmethod
    def d_ret_macd_02_cross_sig_price_ge4(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd
        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_macd_02_cross_sig_price_ge5(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd
        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(10)
    @staticmethod
    def d_ret_macd_02_cross_sig_price_ge6(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd

        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_macd_02_cross_sig_price_he1(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd
        c = d.close
        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(110)
    @staticmethod
    def d_ret_macd_02_cross_sig_price_he2(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd
        c = d.close
        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ###############################################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_ma120_macd_02_cross_sig_price_he1(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 创建买入信号
        condu = ((c > ma120) & (macd > 0) & (macd > signal) &
                 (macd.shift(1) <= signal.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (macd < 0) & (macd < signal) &
                 (macd.shift(1) >= signal.shift(1))).astype(int)

        s = condu - condd

        return s

    @fill_window(77)
    @staticmethod
    def d_ret_ma120_macd_02_cross_sig_price_he2(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 创建买入信号
        condu = ((c > ma120) & (macd > 0) & (macd > signal) &
                 (macd.shift(1) <= signal.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (macd < 0) & (macd < signal) &
                 (macd.shift(1) >= signal.shift(1))).astype(int)

        s = condu - condd

        return s

    @fill_window(15)
    @staticmethod
    def d_ret_ma120_macd_02_cross_sig_price_gong1(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 创建买入信号
        condu = ((c > ma120) & (macd > 0) & (macd > signal) &
                 (macd.shift(1) <= signal.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (macd < 0) & (macd < signal) &
                 (macd.shift(1) >= signal.shift(1))).astype(int)

        s = condu - condd

        c = d.close

        high = c.rolling(window=60).max()
        low = c.rolling(window=60).min()
        mid = (high + low) / 2

        f1 = c / mid
        s[f1 < 1] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_cci_fibonacci_signals_he1(d):
        c = d.close
        high = d.high
        low = d.low

    # 计算CCI（Commodity Channel Index）
        period = 14
        typical_price = (high + low + c) / 3  # 典型价格
        sma = typical_price.rolling(window=period).mean()  # 典型价格的简单移动平均
        mean_deviation = (
            typical_price - sma).abs().rolling(window=period).mean()  # 平均偏差
        cci = (typical_price - sma) / (0.015 * mean_deviation)  # CCI公式

        # 计算斐波那契回撤水平
        fib_levels = [0.236, 0.382, 0.5, 0.618]  # 常见的斐波那契回撤水平
        fib_retracements = pd.DataFrame(index=c.index)

        # 计算每个周期的高低价差
        high_max = high.rolling(window=period).max()
        low_min = low.rolling(window=period).min()

        # 计算斐波那契回撤位
        fib_retracements['fib_23_6'] = high_max - \
            (high_max - low_min) * fib_levels[0]
        fib_retracements['fib_38_2'] = high_max - \
            (high_max - low_min) * fib_levels[1]
        fib_retracements['fib_50_0'] = high_max - \
            (high_max - low_min) * fib_levels[2]
        fib_retracements['fib_61_8'] = high_max - \
            (high_max - low_min) * fib_levels[3]

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(c)):
            # 如果CCI大于100且当前价格接近斐波那契回撤23.6%或者38.2%
            if cci[i] > 100 and (abs(c[i] - fib_retracements['fib_23_6'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                 abs(c[i] - fib_retracements['fib_38_2'][i]) < 0.02 * (high_max[i] - low_min[i])):
                sell_signal[i] = -1  # 卖出信号

            # 如果CCI小于-100且当前价格接近斐波那契回撤61.8%或者50%
            elif cci[i] < -100 and (abs(c[i] - fib_retracements['fib_61_8'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                    abs(c[i] - fib_retracements['fib_50_0'][i]) < 0.02 * (high_max[i] - low_min[i])):
                buy_signal[i] = 1  # 买入信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        return s

    @fill_window(80)
    @staticmethod
    def d_ret_cci_fibonacci_signals_he2(d):
        c = d.close
        high = d.high
        low = d.low

    # 计算CCI（Commodity Channel Index）
        period = 14
        typical_price = (high + low + c) / 3  # 典型价格
        sma = typical_price.rolling(window=period).mean()  # 典型价格的简单移动平均
        mean_deviation = (
            typical_price - sma).abs().rolling(window=period).mean()  # 平均偏差
        cci = (typical_price - sma) / (0.015 * mean_deviation)  # CCI公式

        # 计算斐波那契回撤水平
        fib_levels = [0.236, 0.382, 0.5, 0.618]  # 常见的斐波那契回撤水平
        fib_retracements = pd.DataFrame(index=c.index)

        # 计算每个周期的高低价差
        high_max = high.rolling(window=period).max()
        low_min = low.rolling(window=period).min()

        # 计算斐波那契回撤位
        fib_retracements['fib_23_6'] = high_max - \
            (high_max - low_min) * fib_levels[0]
        fib_retracements['fib_38_2'] = high_max - \
            (high_max - low_min) * fib_levels[1]
        fib_retracements['fib_50_0'] = high_max - \
            (high_max - low_min) * fib_levels[2]
        fib_retracements['fib_61_8'] = high_max - \
            (high_max - low_min) * fib_levels[3]

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(c)):
            # 如果CCI大于100且当前价格接近斐波那契回撤23.6%或者38.2%
            if cci[i] > 100 and (abs(c[i] - fib_retracements['fib_23_6'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                 abs(c[i] - fib_retracements['fib_38_2'][i]) < 0.02 * (high_max[i] - low_min[i])):
                sell_signal[i] = -1  # 卖出信号

            # 如果CCI小于-100且当前价格接近斐波那契回撤61.8%或者50%
            elif cci[i] < -100 and (abs(c[i] - fib_retracements['fib_61_8'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                    abs(c[i] - fib_retracements['fib_50_0'][i]) < 0.02 * (high_max[i] - low_min[i])):
                buy_signal[i] = 1  # 买入信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        return s

    @fill_window(100)
    @staticmethod
    def d_ret_ma20_volume_cross_signals_gong1(d):
        # 获取收盘价和交易量数据
        c = d.close
        volume = d.volume

        # 计算短期和长期的MA
        short_window = 50  # 短期MA窗口
        long_window = 200  # 长期MA窗口
        MA_short = c.rolling(window=short_window).mean()  # 短期MA
        MA_long = c.rolling(window=long_window).mean()  # 长期MA

        # 计算前300根K线的平均交易量
        avg_volume = volume.rolling(window=300).mean()

        # 初始化信号列，0代表没有信号
        signals = pd.Series(0, index=c.index)

        # 遍历数据生成买卖信号
        for i in range(1, len(c)):
            # 金叉条件：短期MA上穿长期MA且当前为阳线，且当前阳线收盘价的交易量大于前300根K线的平均交易量
            if MA_short.iloc[i] > MA_long.iloc[i] and MA_short.iloc[i-1] <= MA_long.iloc[i-1]:
                if c[i] > c[i-1] and volume[i] > avg_volume.iloc[i]:
                    signals[i] = 1  # 买入信号

        # 死叉条件：短期MA下穿长期MA且当前为阴线，且当前阴线收盘价的交易量大于前300根K线的平均交易量
            elif MA_short.iloc[i] < MA_long.iloc[i] and MA_short.iloc[i-1] >= MA_long.iloc[i-1]:
                if c[i] < c[i-1] and volume[i] > avg_volume.iloc[i]:
                    signals[i] = -1  # 卖出信号

        return signals

    @fill_window(1)
    @staticmethod
    def d_ret_ma20_rsi_macd_cross_sig_price_ge1(d):

        c = d.close

        # 计算 CCI
        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        rsi = compute_rsi(d, 20)
        # cci = (c - c.rolling(window=20).mean()) / (0.015 * c.rolling(window=20).std())

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 计算 MA20 和 MA120
        ma20 = c.rolling(window=20).mean()
        ma120 = c.rolling(window=120).mean()

        # 创建买入信号
        condu = ((rsi < 20) & (ma20 > ma120) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((rsi > 80) & (ma20 < ma120) & (macd < signal)).astype(int)

        s = condu - condd

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_ma20_rsi_macd_cross_sig_price_he1(d):

        c = d.close

        # 计算 CCI
        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        rsi = compute_rsi(d, 20)
        # cci = (c - c.rolling(window=20).mean()) / (0.015 * c.rolling(window=20).std())

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 计算 MA20 和 MA120
        ma20 = c.rolling(window=20).mean()
        ma120 = c.rolling(window=120).mean()

        # 创建买入信号
        condu = ((rsi < 20) & (ma20 > ma120) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((rsi > 80) & (ma20 < ma120) & (macd < signal)).astype(int)

        s = condu - condd

        return s

    @fill_window(144)
    @staticmethod
    def d_ret_ma20_rsi_macd_cross_sig_price_he2(d):

        c = d.close

        # 计算 CCI
        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        rsi = compute_rsi(d, 20)
        # cci = (c - c.rolling(window=20).mean()) / (0.015 * c.rolling(window=20).std())

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 计算 MA20 和 MA120
        ma20 = c.rolling(window=20).mean()
        ma120 = c.rolling(window=120).mean()

        # 创建买入信号
        condu = ((rsi < 20) & (ma20 > ma120) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((rsi > 80) & (ma20 < ma120) & (macd < signal)).astype(int)

        s = condu - condd

        return s

    @fill_window(56)
    @staticmethod
    def d_generate_bbi_factor_hou1(d):
        c = d['close']

    # BBI计算：BBI是多个周期均线的平均
        ma_3 = c.rolling(window=3).mean()   # 3周期均线
        ma_6 = c.rolling(window=6).mean()   # 6周期均线
        ma_12 = c.rolling(window=12).mean()  # 12周期均线
        ma_24 = c.rolling(window=24).mean()  # 24周期均线

        d['BBI'] = (ma_3 + ma_6 + ma_12 + ma_24) / 4  # 计算BBI线

    # 买入信号条件：价格由下向上突破BBI线
        buy_signal = (c > d['BBI']) & (c.shift(1) <= d['BBI'].shift(1))

    # 卖出信号条件：价格由上向下跌破BBI线
        sell_signal = (c < d['BBI']) & (c.shift(1) >= d['BBI'].shift(1))

    # 初始化信号：0 表示无信号
        s = pd.Series(0, index=d.index)

        # 设置买入和卖出信号
        s[buy_signal] = 1   # 买入信号
        s[sell_signal] = -1  # 卖出信号

        c = d.close

        short_T = 5
        long_T = 50
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ################################################

        f_lower = f.expanding().quantile(0.2)
        s[f > f_lower] = 0

        return s

    @fill_window(120)
    @staticmethod
    def d_generate_bbi_factor_gong1(d):
        c = d['close']

    # BBI计算：BBI是多个周期均线的平均
        ma_3 = c.rolling(window=3).mean()   # 3周期均线
        ma_6 = c.rolling(window=6).mean()   # 6周期均线
        ma_12 = c.rolling(window=12).mean()  # 12周期均线
        ma_24 = c.rolling(window=24).mean()  # 24周期均线

        d['BBI'] = (ma_3 + ma_6 + ma_12 + ma_24) / 4  # 计算BBI线

    # 买入信号条件：价格由下向上突破BBI线
        buy_signal = (c > d['BBI']) & (c.shift(1) <= d['BBI'].shift(1))

    # 卖出信号条件：价格由上向下跌破BBI线
        sell_signal = (c < d['BBI']) & (c.shift(1) >= d['BBI'].shift(1))

    # 初始化信号：0 表示无信号
        s = pd.Series(0, index=d.index)

        # 设置买入和卖出信号
        s[buy_signal] = 1   # 买入信号
        s[sell_signal] = -1  # 卖出信号

        c = d.close

        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f1 = ma_short / ma_long
        s[f1 > 0.985] = 0
        ######################################################
        price_ratio = d['close'] / d['open']

        is_bullish = price_ratio > 1.0001  # 阳线
        is_bearish = price_ratio < 0.9999  # 阴线

        bullish_count = is_bullish.rolling(window=60).sum()
        bearish_count = is_bearish.rolling(window=60).sum()

        f2 = bullish_count / (bullish_count + bearish_count)
        s[f2 < 0.51] = 0

        return s

    @fill_window(80)
    @staticmethod
    def d_ret_ma50_cross_sig_price_ge1(d):
        c = d.close
        volume = d.volume
        long_T = 30
        short_T = 5

        # 计算移动平均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算EMV
        emv = (c.diff() / c.shift(1) * volume).rolling(window=short_T).sum()

        # 判断移动平均线是否大于50
        ma_condition = ma_long > 50

        # 判断EMV是否穿越0轴
        emv_cross_up = (emv > 0) & (emv.shift(1) <= 0)  # EMV由下往上穿越0轴
        emv_cross_down = (emv < 0) & (emv.shift(1) >= 0)  # EMV由上往下穿越0轴

        # 生成信号
        buy_signal = ma_condition & emv_cross_up  # 中期买进信号
        sell_signal = ma_condition & emv_cross_down  # 中期卖出信号

        # 将信号转换为数值
        s = buy_signal.astype(int) - sell_signal.astype(int)

        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s

    @fill_window(30)
    @staticmethod
    def d_ret_ma_bbi_rsi_sig_price_gong1(d):
        c = d.close      # 收盘价
        v = d.volume     # 成交量
        long_T = 30      # 长期均线窗口
        short_T = 5      # 短期均线窗口
        kdj_period = 14  # KDJ指标的计算周期
        bbi_short_T = 5  # BBI的短期均线周期
        bbi_long_T = 10  # BBI的长期均线周期

        # 计算短期和长期的移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算KDJ指标
        low_min = c.rolling(window=kdj_period).min()
        high_max = c.rolling(window=kdj_period).max()
        rsv = 100 * (c - low_min) / (high_max - low_min)  # RSV值
        k = rsv.ewm(com=2, adjust=False).mean()  # K值
        d = k.ewm(com=2, adjust=False).mean()  # D值
        j = 3 * k - 2 * d  # J值

        # KDJ金叉条件：K线突破D线且KDJ值小于25
        kdj_cross_up = (k > d) & (k.shift(1) <= d.shift(1))  # KDJ金叉
        kdj_below_25 = j < 25  # KDJ值小于25
        buy_signal_kdj = kdj_cross_up & kdj_below_25

        # KDJ死叉条件：D线突破K线且KDJ值大于75
        kdj_cross_down = (k < d) & (k.shift(1) >= d.shift(1))  # KDJ死叉
        kdj_above_75 = j > 75  # KDJ值大于75
        sell_signal_kdj = kdj_cross_down & kdj_above_75

        # 计算BBI（买卖平衡指数）
        ma_5 = c.rolling(window=5).mean()
        ma_10 = c.rolling(window=10).mean()
        ma_20 = c.rolling(window=20).mean()
        bbi = (ma_5 + ma_10 + ma_20) / 3  # BBI = (5日均线 + 10日均线 + 20日均线) / 3

        # BBI突破双底颈线：BBI突破前期低点
        bbi_double_bottom = bbi > bbi.shift(1)  # BBI突破前一日

        # 买入信号：MA在50之上且KDJ金叉且KDJ小于25，同时BBI突破双底颈线
        buy_signal_bbi = (ma_short > 50) & buy_signal_kdj & bbi_double_bottom

        # 卖出信号：KDJ死叉且KDJ大于75
        sell_signal_bbi = sell_signal_kdj

        # 合并信号：1表示买入，-1表示卖出，0表示没有信号
        s = buy_signal_bbi.astype(int) - sell_signal_bbi.astype(int)

        return s

    @fill_window(50)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_ge1(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)
        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_ge2(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(20)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_ge3(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(60)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_ge4(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_ge5(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(10)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_ge6(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_hou1(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        c = d.close

        short_T = 5
        long_T = 50
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ################################################

        f_upper = f.expanding().quantile(0.8)
        #  signals[f_0 < f_upper] = 0
        s[f < f_upper] = 0

        return s

    @fill_window(20)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_hou2(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)
        c = d.close

        short_T = 5
        long_T = 50
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ################################################

        f_upper = f.expanding().quantile(0.8)
        #  signals[f_0 < f_upper] = 0
        s[f < f_upper] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_hou3(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)
        c = d.close
        h = d.high
        l = d.low

        f = ((c - l) / (h - l))
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(20)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_hou4(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)
        c = d.close
        h = d.high
        l = d.low

        f = ((c - l) / (h - l))
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(103)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_hou5(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)
        c = d.close
        h = d.high
        l = d.low

        f = (h - l) / l
        ################################################

        f_lower = f.expanding().quantile(0.2)
        s[f > f_lower] = 0
        return s

    @fill_window(100)
    @staticmethod
    def d_ret_ma_cci_sig_hou1(d):
        """
        Generates trading signals based on the MA level and CCI threshold:
        - Long entry if MA is above 50 and CCI > 100.
        - Short entry if MA is below 50 and CCI < -100.
        """
        close = d.close
        high = d.high
        low = d.low

        # Parameters
        ma_period = 50  # Moving Average period for trend detection
        cci_period = 20  # CCI period (commonly used)
        cci_threshold = 100  # Threshold for CCI

        # Calculate MA
        ma = close.rolling(window=ma_period).mean()

        # Calculate CCI
        tp = (high + low + close) / 3  # Typical Price for CCI calculation
        cci = (tp - tp.rolling(window=cci_period).mean()) / \
            (0.015 * tp.rolling(window=cci_period).std())

        # Generate Signals
        long_entry = ((ma > 50) & (cci > cci_threshold)).astype(int)
        short_entry = ((ma < 50) & (cci < -cci_threshold)).astype(int)

        # Signal output: 1 for long, -1 for short
        s = long_entry - short_entry
        h = d.high
        l = d.low

        f = (h - l) / l
        ################################################

        f_lower = f.expanding().quantile(0.2)
        s[f > f_lower] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_ma_cci_sig_gong1(d):
        """
        Generates trading signals based on the MA level and CCI threshold:
        - Long entry if MA is above 50 and CCI > 100.
        - Short entry if MA is below 50 and CCI < -100.
        """
        close = d.close
        high = d.high
        low = d.low

        # Parameters
        ma_period = 50  # Moving Average period for trend detection
        cci_period = 20  # CCI period (commonly used)
        cci_threshold = 100  # Threshold for CCI

        # Calculate MA
        ma = close.rolling(window=ma_period).mean()

        # Calculate CCI
        tp = (high + low + close) / 3  # Typical Price for CCI calculation
        cci = (tp - tp.rolling(window=cci_period).mean()) / \
            (0.015 * tp.rolling(window=cci_period).std())

        # Generate Signals
        long_entry = ((ma > 50) & (cci > cci_threshold)).astype(int)
        short_entry = ((ma < 50) & (cci < -cci_threshold)).astype(int)

        # Signal output: 1 for long, -1 for short
        s = long_entry - short_entry

        return s

    @fill_window(50)
    @staticmethod
    def d_ret_ma_short_long_cross_sig_price_hou1(d):
        c = d.close
        long_T1 = 30  # 长期平均线1
        long_T2 = 60  # 长期平均线2
        short_T = 5   # 短期平均线

        # 计算短期和长期移动平均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long1 = c.rolling(window=long_T1).mean()
        ma_long2 = c.rolling(window=long_T2).mean()

        # 确保长期移动平均线大于50
        ma_condition = (ma_long1 > 50) & (ma_long2 > 50)

        # 判断短期平均线是否上穿长期平均线
        crossover_signal1 = (ma_short > ma_long1) & (
            ma_short.shift(1) <= ma_long1.shift(1))
        crossover_signal2 = (ma_short > ma_long2) & (
            ma_short.shift(1) <= ma_long2.shift(1))

        # 结合条件，产生最终信号
        s = ma_condition.astype(
            int) * (crossover_signal1 | crossover_signal2).astype(int)
        c = d.close
        h = d.high
        l = d.low

        f = ((c - l) / (h - l))
        ################################################

        f_lower = f.expanding().quantile(0.2)
        s[f > f_lower] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_dpo_ma_cross_sig_price_hou1(d):
        c = d.close
        long_T = 30  # 长期移动平均的周期
        short_T = 5  # 短期DPO的周期
        ma_T = 10  # 用来判断趋势的移动平均周期

        # 计算 DPO 值：当前价格 - 长期移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        dpo = c - ma_short  # DPO是当前价格与短期移动平均的差值

        # 计算MA值来判断趋势方向
        ma = c.rolling(window=ma_T).mean()  # 用ma_T来计算一个中期的简单移动平均

        # 买卖信号：
        # 1. DPO值为正并且价格高于MA时，认为是买入信号
        # 2. DPO值为负并且价格低于MA时，认为是卖出信号
        buy_signal = (dpo > 0) & (c > ma)
        sell_signal = (dpo < 0) & (c < ma)

        # 输出信号，买入信号为1，卖出信号为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)
        c = d.close

        # 衡量短线波动快慢的，快慢线比例
        short_T = 5
        long_T = 50
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(20)
    @staticmethod
    def d_ret_dpo_ma_cross_sig_price_hou2(d):
        c = d.close
        long_T = 30  # 长期移动平均的周期
        short_T = 5  # 短期DPO的周期
        ma_T = 10  # 用来判断趋势的移动平均周期

        # 计算 DPO 值：当前价格 - 长期移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        dpo = c - ma_short  # DPO是当前价格与短期移动平均的差值

        # 计算MA值来判断趋势方向
        ma = c.rolling(window=ma_T).mean()  # 用ma_T来计算一个中期的简单移动平均

        # 买卖信号：
        # 1. DPO值为正并且价格高于MA时，认为是买入信号
        # 2. DPO值为负并且价格低于MA时，认为是卖出信号
        buy_signal = (dpo > 0) & (c > ma)
        sell_signal = (dpo < 0) & (c < ma)

        # 输出信号，买入信号为1，卖出信号为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)
        c = d.close

        # 衡量短线波动快慢的，快慢线比例
        short_T = 5
        long_T = 50
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(20)
    @staticmethod
    def d_ret_dpo_ma_cross_sig_price_hou3(d):
        c = d.close
        long_T = 30  # 长期移动平均的周期
        short_T = 5  # 短期DPO的周期
        ma_T = 10  # 用来判断趋势的移动平均周期

        # 计算 DPO 值：当前价格 - 长期移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        dpo = c - ma_short  # DPO是当前价格与短期移动平均的差值

        # 计算MA值来判断趋势方向
        ma = c.rolling(window=ma_T).mean()  # 用ma_T来计算一个中期的简单移动平均

        # 买卖信号：
        # 1. DPO值为正并且价格高于MA时，认为是买入信号
        # 2. DPO值为负并且价格低于MA时，认为是卖出信号
        buy_signal = (dpo > 0) & (c > ma)
        sell_signal = (dpo < 0) & (c < ma)

        # 输出信号，买入信号为1，卖出信号为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)
        c = d.close
        h = d.high
        l = d.low

        f = ((c - l) / (h - l))
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(120)
    @staticmethod
    def d_ret_dpo_ma_cross_sig_price_gong1(d):
        c = d.close
        long_T = 30  # 长期移动平均的周期
        short_T = 5  # 短期DPO的周期
        ma_T = 10  # 用来判断趋势的移动平均周期

        # 计算 DPO 值：当前价格 - 长期移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        dpo = c - ma_short  # DPO是当前价格与短期移动平均的差值

        # 计算MA值来判断趋势方向
        ma = c.rolling(window=ma_T).mean()  # 用ma_T来计算一个中期的简单移动平均

        # 买卖信号：
        # 1. DPO值为正并且价格高于MA时，认为是买入信号
        # 2. DPO值为负并且价格低于MA时，认为是卖出信号
        buy_signal = (dpo > 0) & (c > ma)
        sell_signal = (dpo < 0) & (c < ma)

        # 输出信号，买入信号为1，卖出信号为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)
        price_ratio = d['close'] / d['open']

        is_bullish = price_ratio > 1.0001  # 阳线
        is_bearish = price_ratio < 0.9999  # 阴线

        bullish_count = is_bullish.rolling(window=60).sum()
        bearish_count = is_bearish.rolling(window=60).sum()

        f1 = bullish_count / (bullish_count + bearish_count)
        s[f1 < 0.55] = 0
        ######################################################
        c = d.close

        high = c.rolling(window=60).max()
        low = c.rolling(window=60).min()
        mid = (high + low) / 2

        f2 = c / mid
        s[f2 < 1.02] = 0
        return s

    @fill_window(80)
    @staticmethod
    def d_ret_po_signals_ge1(d, short_period=9, long_period=26):
        c = d.close

        # 计算短期和长期 EMA
        ema_short = c.ewm(span=short_period, adjust=False).mean()
        ema_long = c.ewm(span=long_period, adjust=False).mean()

        # 计算 PO
        po = (ema_short - ema_long) / ema_long * 100

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(po)):
            # PO 上穿 0 时，产生买入信号
            if po[i - 1] <= 0 and po[i] > 0:
                buy_signal[i] = 1  # 买入信号

            # PO 下穿 0 时，产生卖出信号
            elif po[i - 1] >= 0 and po[i] < 0:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal
        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0
        return s

    @fill_window(220)
    @staticmethod
    def d_ret_po_signals_ge2(d, short_period=9, long_period=26):
        c = d.close

        # 计算短期和长期 EMA
        ema_short = c.ewm(span=short_period, adjust=False).mean()
        ema_long = c.ewm(span=long_period, adjust=False).mean()

        # 计算 PO
        po = (ema_short - ema_long) / ema_long * 100

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(po)):
            # PO 上穿 0 时，产生买入信号
            if po[i - 1] <= 0 and po[i] > 0:
                buy_signal[i] = 1  # 买入信号

            # PO 下穿 0 时，产生卖出信号
            elif po[i - 1] >= 0 and po[i] < 0:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal
        c = d.close

        long_T = 20
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(58)
    @staticmethod
    def d_ret_po_signals_hou1(d, short_period=9, long_period=26):
        c = d.close

        # 计算短期和长期 EMA
        ema_short = c.ewm(span=short_period, adjust=False).mean()
        ema_long = c.ewm(span=long_period, adjust=False).mean()

        # 计算 PO
        po = (ema_short - ema_long) / ema_long * 100

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(po)):
            # PO 上穿 0 时，产生买入信号
            if po[i - 1] <= 0 and po[i] > 0:
                buy_signal[i] = 1  # 买入信号

            # PO 下穿 0 时，产生卖出信号
            elif po[i - 1] >= 0 and po[i] < 0:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal
        c = d.close
        h = d.high
        l = d.low

        f = ((c - l) / (h - l))
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(110)
    @staticmethod
    def d_ret_rma_cross_sig_price_gong1(d):
        """
        基于 RMA 指标的多空信号策略
        :param d: DataFrame 包含 'close', 'high', 'low', 'volume' 列
        :return: 信号序列，多头为 1，空头为 -1，无信号为 0
        """
        c = d.close
        long_T = 30  # 长期周期
        short_T = 5  # 短期周期

        # 定义 RMA 计算公式
        def rma(series, window):
            return series.ewm(alpha=1/window, adjust=False).mean()

        # 计算短期和长期 RMA
        rma_short = rma(c, short_T)
        rma_long = rma(c, long_T)

        # 计算高点和低点的 RMA
        high_short = rma(d.high, short_T)
        high_long = rma(d.high, long_T)
        low_short = rma(d.low, short_T)
        low_long = rma(d.low, long_T)

        # 计算短期和长期的成交量 RMA
        vol_short = rma(d.volume, short_T)
        vol_long = rma(d.volume, long_T)

        # 多头信号条件：短期 RMA 上穿长期 RMA 且短期高点高于长期高点
        condu = ((((rma_short > rma_long).astype(int).diff() == 1).astype(int) +
                  (high_short >= high_long).astype(int)) == 2).astype(int)

        # 空头信号条件：短期 RMA 下穿长期 RMA 且短期低点低于长期低点
        condd = ((((rma_short < rma_long).astype(int).diff() == 1).astype(int) +
                  (low_short <= low_long).astype(int)) == 2).astype(int)

        # 计算成交量的比率
        ft = vol_short / vol_long

    # 返回信号（多头为 1，空头为 -1，无信号为 0）
        s = condu - condd

        c = d.close

        high = c.rolling(window=60).max()
        low = c.rolling(window=60).min()
        mid = (high + low) / 2

        f1 = c / mid
        s[f1 < 1] = 0
        return s

    @fill_window(40)
    @staticmethod
    def d_ret_ma120_bbi_signals_ge1(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

    # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

    # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

    # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        s = buy_signal - sell_signal

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_ma120_bbi_signals_ge2(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

    # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

    # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

    # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        s = buy_signal - sell_signal
        c = d.close

        long_T = 120
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(5)
    @staticmethod
    def d_ret_ma120_bbi_signals_ge3(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

    # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

    # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

    # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        s = buy_signal - sell_signal

        c = d.close

        long_T = 120
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(40)
    @staticmethod
    def d_ret_ma120_bbi_signals_ge4(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

    # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

    # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

    # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        s = buy_signal - sell_signal

        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_ma120_bbi_signals_ge5(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

    # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

    # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

    # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        s = buy_signal - sell_signal

        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(10)
    @staticmethod
    def d_ret_ma120_bbi_signals_ge6(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

    # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

    # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

    # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        s = buy_signal - sell_signal

        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_ma120_bbi_signals_hou1(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

    # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

    # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

    # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        s = buy_signal - sell_signal
        c = d.close

        # 衡量短线波动快慢的，快慢线比例
        short_T = 5
        long_T = 50
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(100)
    @staticmethod
    def d_ret_ma120_bbi_signals_hou2(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

    # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

    # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

    # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        s = buy_signal - sell_signal
        c = d.close

        # 衡量短线波动快慢的，快慢线比例
        short_T = 5
        long_T = 50
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_ma120_bbi_signals_hou3(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

    # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

    # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

    # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        s = buy_signal - sell_signal
        c = d.close
        h = d.high
        l = d.low

        f = ((c - l) / (h - l))
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(20)
    @staticmethod
    def d_ret_ma120_bbi_signals_hou4(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

    # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

    # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

    # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        s = buy_signal - sell_signal
        c = d.close
        h = d.high
        l = d.low

        f = ((c - l) / (h - l))
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_skdj_sig_price_hou1(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # SKDJ的标准计算周期

        # 计算SKDJ的 %K 和 %D 线
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        k_line = ((c - lowest_low) / (highest_high - lowest_low)) * 100
        d_line = k_line.rolling(window=3).mean()

        # 金叉买入信号：%K 上穿 %D 且都小于 25
        golden_cross_buy = ((k_line.shift(1) < d_line.shift(1)) & (
            k_line >= d_line) & (k_line < 25) & (d_line < 25)).astype(int)

        # 死叉卖出信号：%K 下穿 %D 且都大于 75
        death_cross_sell = ((k_line.shift(1) > d_line.shift(1)) & (
            k_line <= d_line) & (k_line > 75) & (d_line > 75)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        c = d.close
        h = d.high
        l = d.low

        f = ((c - l) / (h - l))
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0

        return s

    @fill_window(100)
    @staticmethod
    def d_ret_skdj_sig_price_hou2(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # SKDJ的标准计算周期

        # 计算SKDJ的 %K 和 %D 线
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        k_line = ((c - lowest_low) / (highest_high - lowest_low)) * 100
        d_line = k_line.rolling(window=3).mean()

        # 金叉买入信号：%K 上穿 %D 且都小于 25
        golden_cross_buy = ((k_line.shift(1) < d_line.shift(1)) & (
            k_line >= d_line) & (k_line < 25) & (d_line < 25)).astype(int)

        # 死叉卖出信号：%K 下穿 %D 且都大于 75
        death_cross_sell = ((k_line.shift(1) > d_line.shift(1)) & (
            k_line <= d_line) & (k_line > 75) & (d_line > 75)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell
        c = d.close
        h = d.high
        l = d.low

        f = ((c - l) / (h - l))
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(50)
    @staticmethod
    def d_ret_skdj_sig_price_gong1(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # SKDJ的标准计算周期

        # 计算SKDJ的 %K 和 %D 线
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        k_line = ((c - lowest_low) / (highest_high - lowest_low)) * 100
        d_line = k_line.rolling(window=3).mean()

        # 金叉买入信号：%K 上穿 %D 且都小于 25
        golden_cross_buy = ((k_line.shift(1) < d_line.shift(1)) & (
            k_line >= d_line) & (k_line < 25) & (d_line < 25)).astype(int)

        # 死叉卖出信号：%K 下穿 %D 且都大于 75
        death_cross_sell = ((k_line.shift(1) > d_line.shift(1)) & (
            k_line <= d_line) & (k_line > 75) & (d_line > 75)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        return s

    @fill_window(35)
    @staticmethod
    def d_ret_wma_signals_hou1(d):

        def weighted_moving_average(series, window):
            weights = range(1, window + 1)  # 权重从1到window
            return series.rolling(window=window).apply(lambda x: (x * weights).sum() / sum(weights), raw=True)

        c = d['close']  # 收盘价格
        short_T = 5  # 短期WMA窗口
        long_T = 30  # 长期WMA窗口

        # 计算短期和长期的WMA
        wma_short = weighted_moving_average(c, short_T)
        wma_long = weighted_moving_average(c, long_T)

    # 做多买入信号：短期WMA突破长期WMA
        buy_signal = ((wma_short > wma_long) & (
            wma_short.shift(1) <= wma_long.shift(1))).astype(int)

    # 做空卖出信号：短期WMA跌破长期WMA
        sell_signal = ((wma_short < wma_long) & (
            wma_short.shift(1) >= wma_long.shift(1))).astype(int)

    # 最终信号：1为买入信号，-1为卖出信号，0为无信号
        s = buy_signal - sell_signal
        c = d.close
        h = d.high
        l = d.low

        f = ((c - l) / (h - l))
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(130)
    @staticmethod
    def d_ret_wma_signals_gong1(d):

        def weighted_moving_average(series, window):
            weights = range(1, window + 1)  # 权重从1到window
            return series.rolling(window=window).apply(lambda x: (x * weights).sum() / sum(weights), raw=True)

        c = d['close']  # 收盘价格
        short_T = 5  # 短期WMA窗口
        long_T = 30  # 长期WMA窗口

        # 计算短期和长期的WMA
        wma_short = weighted_moving_average(c, short_T)
        wma_long = weighted_moving_average(c, long_T)

    # 做多买入信号：短期WMA突破长期WMA
        buy_signal = ((wma_short > wma_long) & (
            wma_short.shift(1) <= wma_long.shift(1))).astype(int)

    # 做空卖出信号：短期WMA跌破长期WMA
        sell_signal = ((wma_short < wma_long) & (
            wma_short.shift(1) >= wma_long.shift(1))).astype(int)

    # 最终信号：1为买入信号，-1为卖出信号，0为无信号
        s = buy_signal - sell_signal
        c = d.close

        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f1 = ma_short / ma_long
        s[f1 > 0.98] = 0
        return s

    @fill_window(40)
    @staticmethod
    def d_ret_rsi_bb_ma_signal_ge1(d):

        def calculate_rsi(series, period=14):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).rolling(window=period).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

        c = d.close
        rsi = calculate_rsi(c)
        mid_band = c.rolling(20).mean()
        upper_band = mid_band + 2 * c.rolling(20).std()
        lower_band = mid_band - 2 * c.rolling(20).std()
        ma120 = c.rolling(120).mean()

        buy_signal = ((rsi > 50) & (c > mid_band) & (
            c.shift(1) <= mid_band)).astype(int)
        add_position_signal = ((buy_signal.cumsum() > 0) & (
            c < mid_band) & (c > lower_band)).astype(int)
        sell_signal = (c >= upper_band).astype(int)

        s = buy_signal - sell_signal + add_position_signal
        s[ma120 > c] *= -1  # Reverse signals if below MA120
        c = d.close

        long_T = 120
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0
        return s

    @fill_window(80)
    @staticmethod
    def d_ret_rsi_bb_ma_signal_ge2(d):

        def calculate_rsi(series, period=14):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).rolling(window=period).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

        c = d.close
        rsi = calculate_rsi(c)
        mid_band = c.rolling(20).mean()
        upper_band = mid_band + 2 * c.rolling(20).std()
        lower_band = mid_band - 2 * c.rolling(20).std()
        ma120 = c.rolling(120).mean()

        buy_signal = ((rsi > 50) & (c > mid_band) & (
            c.shift(1) <= mid_band)).astype(int)
        add_position_signal = ((buy_signal.cumsum() > 0) & (
            c < mid_band) & (c > lower_band)).astype(int)
        sell_signal = (c >= upper_band).astype(int)

        s = buy_signal - sell_signal + add_position_signal
        s[ma120 > c] *= -1  # Reverse signals if below MA120
        c = d.close

        long_T = 120
        short_T = 5
        ma_long = c.rolling(window=long_T).mean()
        ma_short = c.rolling(window=short_T).mean()
        f = ma_short / ma_long
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_rsi_bb_ma_signal_ge3(d):

        def calculate_rsi(series, period=14):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).rolling(window=period).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

        c = d.close
        rsi = calculate_rsi(c)
        mid_band = c.rolling(20).mean()
        upper_band = mid_band + 2 * c.rolling(20).std()
        lower_band = mid_band - 2 * c.rolling(20).std()
        ma120 = c.rolling(120).mean()

        buy_signal = ((rsi > 50) & (c > mid_band) & (
            c.shift(1) <= mid_band)).astype(int)
        add_position_signal = ((buy_signal.cumsum() > 0) & (
            c < mid_band) & (c > lower_band)).astype(int)
        sell_signal = (c >= upper_band).astype(int)

        s = buy_signal - sell_signal + add_position_signal
        s[ma120 > c] *= -1  # Reverse signals if below MA120
        c = d.close
        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0
        return s

    @fill_window(60)
    @staticmethod
    def d_ret_rsi_bb_ma_signal_ge4(d):

        def calculate_rsi(series, period=14):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).rolling(window=period).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

        c = d.close
        rsi = calculate_rsi(c)
        mid_band = c.rolling(20).mean()
        upper_band = mid_band + 2 * c.rolling(20).std()
        lower_band = mid_band - 2 * c.rolling(20).std()
        ma120 = c.rolling(120).mean()

        buy_signal = ((rsi > 50) & (c > mid_band) & (
            c.shift(1) <= mid_band)).astype(int)
        add_position_signal = ((buy_signal.cumsum() > 0) & (
            c < mid_band) & (c > lower_band)).astype(int)
        sell_signal = (c >= upper_band).astype(int)

        s = buy_signal - sell_signal + add_position_signal
        s[ma120 > c] *= -1  # Reverse signals if below MA120
        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.2)
        s[f > f_cond] = 0
        return s

    @fill_window(190)
    @staticmethod
    def d_ret_rsi_bb_ma_signal_ge5(d):

        def calculate_rsi(series, period=14):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).rolling(window=period).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

        c = d.close
        rsi = calculate_rsi(c)
        mid_band = c.rolling(20).mean()
        upper_band = mid_band + 2 * c.rolling(20).std()
        lower_band = mid_band - 2 * c.rolling(20).std()
        ma120 = c.rolling(120).mean()

        buy_signal = ((rsi > 50) & (c > mid_band) & (
            c.shift(1) <= mid_band)).astype(int)
        add_position_signal = ((buy_signal.cumsum() > 0) & (
            c < mid_band) & (c > lower_band)).astype(int)
        sell_signal = (c >= upper_band).astype(int)

        s = buy_signal - sell_signal + add_position_signal
        s[ma120 > c] *= -1  # Reverse signals if below MA120
        c = d.close

        window = 240
        mean_price = c.rolling(window=window).mean()
        std_price = c.rolling(window=window).std()

        f = (c - mean_price) / std_price
        ##########################################

        f_cond = f.expanding().quantile(0.8)
        s[f < f_cond] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_rsi_bb_ma_signal_hou1(d):

        def calculate_rsi(series, period=14):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).rolling(window=period).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

        c = d.close
        rsi = calculate_rsi(c)
        mid_band = c.rolling(20).mean()
        upper_band = mid_band + 2 * c.rolling(20).std()
        lower_band = mid_band - 2 * c.rolling(20).std()
        ma120 = c.rolling(120).mean()

        buy_signal = ((rsi > 50) & (c > mid_band) & (
            c.shift(1) <= mid_band)).astype(int)
        add_position_signal = ((buy_signal.cumsum() > 0) & (
            c < mid_band) & (c > lower_band)).astype(int)
        sell_signal = (c >= upper_band).astype(int)

        s = buy_signal - sell_signal + add_position_signal
        s[ma120 > c] *= -1  # Reverse signals if below MA120
        c = d.close
        h = d.high
        l = d.low

        f = (h - l) / l
        ################################################

        f_lower = f.expanding().quantile(0.2)
        s[f > f_lower] = 0
        return s

    @fill_window(30)
    @staticmethod
    def d_ret_rsi_bb_ma_signal_hou2(d):

        def calculate_rsi(series, period=14):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).rolling(window=period).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

        c = d.close
        rsi = calculate_rsi(c)
        mid_band = c.rolling(20).mean()
        upper_band = mid_band + 2 * c.rolling(20).std()
        lower_band = mid_band - 2 * c.rolling(20).std()
        ma120 = c.rolling(120).mean()

        buy_signal = ((rsi > 50) & (c > mid_band) & (
            c.shift(1) <= mid_band)).astype(int)
        add_position_signal = ((buy_signal.cumsum() > 0) & (
            c < mid_band) & (c > lower_band)).astype(int)
        sell_signal = (c >= upper_band).astype(int)

        s = buy_signal - sell_signal + add_position_signal
        s[ma120 > c] *= -1  # Reverse signals if below MA120
        c = d.close
        h = d.high
        l = d.low

        f = (h - l) / l
        ################################################

        f_lower = f.expanding().quantile(0.2)
        s[f > f_lower] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_rsi_bb_ma_signal_hou3(d):

        def calculate_rsi(series, period=14):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).rolling(window=period).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

        c = d.close
        rsi = calculate_rsi(c)
        mid_band = c.rolling(20).mean()
        upper_band = mid_band + 2 * c.rolling(20).std()
        lower_band = mid_band - 2 * c.rolling(20).std()
        ma120 = c.rolling(120).mean()

        buy_signal = ((rsi > 50) & (c > mid_band) & (
            c.shift(1) <= mid_band)).astype(int)
        add_position_signal = ((buy_signal.cumsum() > 0) & (
            c < mid_band) & (c > lower_band)).astype(int)
        sell_signal = (c >= upper_band).astype(int)

        s = buy_signal - sell_signal + add_position_signal
        s[ma120 > c] *= -1  # Reverse signals if below MA120
        c = d.close
        h = d.high
        l = d.low
        v = d.volume

        highest_volume = v.tail(10).max()
        lowest_volume = v.tail(10).min()
        f = (v - lowest_volume) / (highest_volume - lowest_volume)
        ################################################

        f_lower = f.expanding().quantile(0.2)
        s[f > f_lower] = 0
        return s

    @fill_window(100)
    @staticmethod
    def d_ret_rsi_bb_ma_signal_hou4(d):

        def calculate_rsi(series, period=14):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).rolling(window=period).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

        c = d.close
        rsi = calculate_rsi(c)
        mid_band = c.rolling(20).mean()
        upper_band = mid_band + 2 * c.rolling(20).std()
        lower_band = mid_band - 2 * c.rolling(20).std()
        ma120 = c.rolling(120).mean()

        buy_signal = ((rsi > 50) & (c > mid_band) & (
            c.shift(1) <= mid_band)).astype(int)
        add_position_signal = ((buy_signal.cumsum() > 0) & (
            c < mid_band) & (c > lower_band)).astype(int)
        sell_signal = (c >= upper_band).astype(int)

        s = buy_signal - sell_signal + add_position_signal
        s[ma120 > c] *= -1  # Reverse signals if below MA120
        c = d.close
        h = d.high
        l = d.low
        v = d.volume

        highest_volume = v.tail(10).max()
        lowest_volume = v.tail(10).min()
        f = (v - lowest_volume) / (highest_volume - lowest_volume)
        ################################################

        f_lower = f.expanding().quantile(0.2)
        s[f > f_lower] = 0
        return s

    @fill_window(15)
    @staticmethod
    def d_ret_macd_cross_signal_gong1(d):
        c = d.close  # Get closing prices
        short_T = 12  # Short-term EMA period
        long_T = 26   # Long-term EMA period
        signal_T = 9  # Signal line period (MACD signal line)

        # Calculate short-term and long-term EMAs
        ema_short = c.ewm(span=short_T, adjust=False).mean()
        ema_long = c.ewm(span=long_T, adjust=False).mean()

        # Calculate DIF (Difference between short-term and long-term EMAs)
        dif = ema_short - ema_long

        # Calculate MACD (Signal line is an EMA of the DIF)
        macd = dif.ewm(span=signal_T, adjust=False).mean()

        # Calculate the difference between closing price and MACD for bar height
        bar = c - macd

        # Detect Golden Cross (DIF crosses above MACD)
        golden_cross = ((dif > macd) & (
            dif.shift(1) <= macd.shift(1))).astype(int)

        # Detect Death Cross (DIF crosses below MACD)
        death_cross = ((dif < macd) & (
            dif.shift(1) >= macd.shift(1))).astype(int)

        # Detect shrinking bars (current bar is smaller than the previous one)
        bar_shrink = (bar.abs() < bar.shift(1).abs()).astype(int)

        # Buy signal: Golden cross with shrinking green bars
        buy_signal = golden_cross & bar_shrink

        # Sell signal: Death cross with shrinking red bars
        sell_signal = death_cross & bar_shrink

        # Return buy and sell signals
        s = buy_signal - sell_signal
        return s  # 1 for buy, -1 for sell, 0 for no action

    @fill_window(40)
    @staticmethod
    def d_ret_rsi_boll_sig_ge1(d):
        c = d.close
        high = d.high
        low = d.low

        # Parameters for Bollinger Bands and RSI
        rsi_period = 14
        boll_period = 20
        boll_std = 2

        # Calculate the RSI (Relative Strength Index)
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Calculate Bollinger Bands
        rolling_mean = c.rolling(window=boll_period).mean()
        rolling_std = c.rolling(window=boll_period).std()
        boll_upper = rolling_mean + (boll_std * rolling_std)
        boll_lower = rolling_mean - (boll_std * rolling_std)
        boll_middle = rolling_mean

        # Buy signal condition: RSI crosses above 50 and price is above the middle Bollinger Band
        buy_signal = ((rsi.shift(1) <= 50) & (rsi > 50) & (c > boll_middle))

        # Sell signal condition: Price breaks below the middle Bollinger Band and reaches the lower Bollinger Band
        sell_signal = ((c.shift(1) > boll_middle) & (
            c < boll_middle) & (c <= boll_lower))

        # Signal output: 1 for buy, -1 for sell, 0 for no action
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @fill_window(65)
    @staticmethod
    def d_ret_rsi_boll_sig_hou1(d):
        c = d.close
        high = d.high
        low = d.low

        # Parameters for Bollinger Bands and RSI
        rsi_period = 14
        boll_period = 20
        boll_std = 2

        # Calculate the RSI (Relative Strength Index)
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Calculate Bollinger Bands
        rolling_mean = c.rolling(window=boll_period).mean()
        rolling_std = c.rolling(window=boll_period).std()
        boll_upper = rolling_mean + (boll_std * rolling_std)
        boll_lower = rolling_mean - (boll_std * rolling_std)
        boll_middle = rolling_mean

        # Buy signal condition: RSI crosses above 50 and price is above the middle Bollinger Band
        buy_signal = ((rsi.shift(1) <= 50) & (rsi > 50) & (c > boll_middle))

        # Sell signal condition: Price breaks below the middle Bollinger Band and reaches the lower Bollinger Band
        sell_signal = ((c.shift(1) > boll_middle) & (
            c < boll_middle) & (c <= boll_lower))

        # Signal output: 1 for buy, -1 for sell, 0 for no action
        s = buy_signal.astype(int) - sell_signal.astype(int)
        c = d.close

        # 衡量短线波动快慢的，快慢线比例
        short_T = 5
        long_T = 50
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ################################################

        f_lower = f.expanding().quantile(0.2)
        s[f > f_lower] = 0
        return s

    @fill_window(60)
    @staticmethod
    def d_ret_rsi_boll_sig_hou2(d):
        c = d.close
        high = d.high
        low = d.low

        # Parameters for Bollinger Bands and RSI
        rsi_period = 14
        boll_period = 20
        boll_std = 2

        # Calculate the RSI (Relative Strength Index)
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Calculate Bollinger Bands
        rolling_mean = c.rolling(window=boll_period).mean()
        rolling_std = c.rolling(window=boll_period).std()
        boll_upper = rolling_mean + (boll_std * rolling_std)
        boll_lower = rolling_mean - (boll_std * rolling_std)
        boll_middle = rolling_mean

        # Buy signal condition: RSI crosses above 50 and price is above the middle Bollinger Band
        buy_signal = ((rsi.shift(1) <= 50) & (rsi > 50) & (c > boll_middle))

        # Sell signal condition: Price breaks below the middle Bollinger Band and reaches the lower Bollinger Band
        sell_signal = ((c.shift(1) > boll_middle) & (
            c < boll_middle) & (c <= boll_lower))

        # Signal output: 1 for buy, -1 for sell, 0 for no action
        s = buy_signal.astype(int) - sell_signal.astype(int)
        c = d.close
        h = d.high
        l = d.low

        f = (h - l) / l
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(75)
    @staticmethod
    def d_ret_rsi_boll_sig_hou3(d):
        c = d.close
        high = d.high
        low = d.low

        # Parameters for Bollinger Bands and RSI
        rsi_period = 14
        boll_period = 20
        boll_std = 2

        # Calculate the RSI (Relative Strength Index)
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Calculate Bollinger Bands
        rolling_mean = c.rolling(window=boll_period).mean()
        rolling_std = c.rolling(window=boll_period).std()
        boll_upper = rolling_mean + (boll_std * rolling_std)
        boll_lower = rolling_mean - (boll_std * rolling_std)
        boll_middle = rolling_mean

        # Buy signal condition: RSI crosses above 50 and price is above the middle Bollinger Band
        buy_signal = ((rsi.shift(1) <= 50) & (rsi > 50) & (c > boll_middle))

        # Sell signal condition: Price breaks below the middle Bollinger Band and reaches the lower Bollinger Band
        sell_signal = ((c.shift(1) > boll_middle) & (
            c < boll_middle) & (c <= boll_lower))

        # Signal output: 1 for buy, -1 for sell, 0 for no action
        s = buy_signal.astype(int) - sell_signal.astype(int)
        c = d.close
        h = d.high
        l = d.low
        v = d.volume

        highest_volume = v.tail(10).max()
        lowest_volume = v.tail(10).min()
        f = (v - lowest_volume) / (highest_volume - lowest_volume)
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(20)
    @staticmethod
    def d_ret_mfi_sig_price_hou1(d):
        print(type(d))
        c = d.close
        high = d.high
        low = d.low
        volume = d.volume

        # MFI 计算
        typical_price = (high + low + c) / 3
        money_flow = typical_price * volume
        positive_flow = (typical_price > typical_price.shift(1)) * money_flow
        negative_flow = (typical_price < typical_price.shift(1)) * money_flow

        # 计算 MFI
        mfi = 100 * positive_flow.rolling(window=14).sum() / (
            positive_flow.rolling(window=14).sum() + negative_flow.rolling(window=14).sum())

        # 买入信号: MFI 上穿 80
        buy_signal = (mfi.shift(1) < 80) & (mfi >= 80)

        # 卖出信号: MFI 下穿 20
        sell_signal = (mfi.shift(1) > 20) & (mfi <= 20)

        # 输出信号：1 表示买入，-1 表示卖出，0 表示无操作
        s = pd.Series(0, index=d.index)
        s[buy_signal] = 1  # 买入信号
        s[sell_signal] = -1  # 卖出信号

        c = d['close']
        # 衡量短线波动快慢的，快慢线比例
        short_T = 5
        long_T = 50
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_mfi_sig_price_gong1(d):
        c = d.close
        high = d.high
        low = d.low
        volume = d.volume

        # MFI 计算
        typical_price = (high + low + c) / 3
        money_flow = typical_price * volume
        positive_flow = (typical_price > typical_price.shift(1)) * money_flow
        negative_flow = (typical_price < typical_price.shift(1)) * money_flow

        # 计算 MFI
        mfi = 100 * positive_flow.rolling(window=14).sum() / (
            positive_flow.rolling(window=14).sum() + negative_flow.rolling(window=14).sum())

        # 买入信号: MFI 上穿 80
        buy_signal = (mfi.shift(1) < 80) & (mfi >= 80)

        # 卖出信号: MFI 下穿 20
        sell_signal = (mfi.shift(1) > 20) & (mfi <= 20)

        # 输出信号：1 表示买入，-1 表示卖出，0 表示无操作
        s = pd.Series(0, index=d.index)
        s[buy_signal] = 1  # 买入信号
        s[sell_signal] = -1  # 卖出信号

        price_ratio = d['close'] / d['open']

        is_bullish = price_ratio > 1.0001  # 阳线
        is_bearish = price_ratio < 0.9999  # 阴线

        bullish_count = is_bullish.rolling(window=60).sum()
        bearish_count = is_bearish.rolling(window=60).sum()

        f1 = bullish_count / (bullish_count + bearish_count)
        s[f1 < 0.55] = 0
        return s

    @fill_window(10)
    @staticmethod
    def d_ret_mfi_sig_price_gong2(d):
        c = d.close
        high = d.high
        low = d.low
        volume = d.volume

        # MFI 计算
        typical_price = (high + low + c) / 3
        money_flow = typical_price * volume
        positive_flow = (typical_price > typical_price.shift(1)) * money_flow
        negative_flow = (typical_price < typical_price.shift(1)) * money_flow

        # 计算 MFI
        mfi = 100 * positive_flow.rolling(window=14).sum() / (
            positive_flow.rolling(window=14).sum() + negative_flow.rolling(window=14).sum())

        # 买入信号: MFI 上穿 80
        buy_signal = (mfi.shift(1) < 80) & (mfi >= 80)

        # 卖出信号: MFI 下穿 20
        sell_signal = (mfi.shift(1) > 20) & (mfi <= 20)

        # 输出信号：1 表示买入，-1 表示卖出，0 表示无操作
        s = pd.Series(0, index=d.index)
        s[buy_signal] = 1  # 买入信号
        s[sell_signal] = -1  # 卖出信号
        price_ratio = d['close'] / d['open']

        is_bullish = price_ratio > 1.0001  # 阳线
        is_bearish = price_ratio < 0.9999  # 阴线

        bullish_count = is_bullish.rolling(window=60).sum()
        bearish_count = is_bearish.rolling(window=60).sum()

        f1 = bullish_count / (bullish_count + bearish_count)
        s[f1 < 0.55] = 0
        return s

        c = d.close
        long_T = 30
        short_T = 5

        # 计算短期和长期的均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算短期和长期的最高价与最低价
        high_short = c.rolling(window=short_T).max()
        high_long = c.rolling(window=long_T).max()
        low_short = c.rolling(window=short_T).min()
        low_long = c.rolling(window=long_T).min()

        # 计算成交量的短期和长期和
        vol_short = c.rolling(window=short_T).sum()
        vol_long = c.rolling(window=long_T).sum()

        # 计算上穿下穿的条件
        condu = ((((ma_short > ma_long).astype(int).diff() == 1).astype(
            int) + (high_short >= high_long).astype(int)) == 2).astype(int)
        condd = ((((ma_short < ma_long).astype(int).diff() == 1).astype(
            int) + (low_short <= low_long).astype(int)) == 2).astype(int)

        # 计算成交量的相对值
        ft = vol_short / vol_long

        # 计算买入卖出信号的基础信号
        s = condu - condd

        # --- STC Indicator Calculation ---

        # 计算快速和慢速移动平均线
        fast_k = c.rolling(window=short_T).mean()  # 快速移动平均
        slow_d = c.rolling(window=long_T).mean()   # 慢速移动平均

        # 计算 STC 值
        stc = 100 * (fast_k - slow_d) / slow_d  # 简化的 STC 公式，实际可根据需要调整

        # 根据 STC 判断买入卖出信号
        buy_signal = (stc > 25).astype(int)  # 上穿 25
        sell_signal = (stc < 75).astype(int)  # 下穿 75

        # 最终的信号：买卖信号
        s = buy_signal - sell_signal
        c = d.close

        # 衡量短线波动快慢的，快慢线比例
        short_T = 5
        long_T = 50
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f = ma_short / ma_long
        ################################################

        f_upper = f.expanding().quantile(0.8)
        s[f < f_upper] = 0
        return s

    @fill_window(1)
    @staticmethod
    def d_ret_stc_sig_price_gong1(d):
        c = d.close
        long_T = 30
        short_T = 5

        # 计算短期和长期的均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算短期和长期的最高价与最低价
        high_short = c.rolling(window=short_T).max()
        high_long = c.rolling(window=long_T).max()
        low_short = c.rolling(window=short_T).min()
        low_long = c.rolling(window=long_T).min()

        # 计算成交量的短期和长期和
        vol_short = c.rolling(window=short_T).sum()
        vol_long = c.rolling(window=long_T).sum()

        # 计算上穿下穿的条件
        condu = ((((ma_short > ma_long).astype(int).diff() == 1).astype(
            int) + (high_short >= high_long).astype(int)) == 2).astype(int)
        condd = ((((ma_short < ma_long).astype(int).diff() == 1).astype(
            int) + (low_short <= low_long).astype(int)) == 2).astype(int)

        # 计算成交量的相对值
        ft = vol_short / vol_long

        # 计算买入卖出信号的基础信号
        s = condu - condd

        # --- STC Indicator Calculation ---

        # 计算快速和慢速移动平均线
        fast_k = c.rolling(window=short_T).mean()  # 快速移动平均
        slow_d = c.rolling(window=long_T).mean()   # 慢速移动平均

        # 计算 STC 值
        stc = 100 * (fast_k - slow_d) / slow_d  # 简化的 STC 公式，实际可根据需要调整

        # 根据 STC 判断买入卖出信号
        buy_signal = (stc > 25).astype(int)  # 上穿 25
        sell_signal = (stc < 75).astype(int)  # 下穿 75

        # 最终的信号：买卖信号
        s = buy_signal - sell_signal
        c = d.close

        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f1 = ma_short / ma_long
        s[f1 < 1.02] = 0
        ######################################################
        price_ratio = d['close'] / d['open']

        is_bullish = price_ratio > 1.0001  # 阳线
        is_bearish = price_ratio < 0.9999  # 阴线

        bullish_count = is_bullish.rolling(window=60).sum()
        bearish_count = is_bearish.rolling(window=60).sum()

        f2 = bullish_count / (bullish_count + bearish_count)
        s[f2 < 0.57] = 0
        return s

    @fill_window(100)
    @staticmethod
    def d_ret_stc_sig_price_gong2(d):
        c = d.close
        long_T = 30
        short_T = 5

        # 计算短期和长期的均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算短期和长期的最高价与最低价
        high_short = c.rolling(window=short_T).max()
        high_long = c.rolling(window=long_T).max()
        low_short = c.rolling(window=short_T).min()
        low_long = c.rolling(window=long_T).min()

        # 计算成交量的短期和长期和
        vol_short = c.rolling(window=short_T).sum()
        vol_long = c.rolling(window=long_T).sum()

        # 计算上穿下穿的条件
        condu = ((((ma_short > ma_long).astype(int).diff() == 1).astype(
            int) + (high_short >= high_long).astype(int)) == 2).astype(int)
        condd = ((((ma_short < ma_long).astype(int).diff() == 1).astype(
            int) + (low_short <= low_long).astype(int)) == 2).astype(int)

        # 计算成交量的相对值
        ft = vol_short / vol_long

        # 计算买入卖出信号的基础信号
        s = condu - condd

        # --- STC Indicator Calculation ---

        # 计算快速和慢速移动平均线
        fast_k = c.rolling(window=short_T).mean()  # 快速移动平均
        slow_d = c.rolling(window=long_T).mean()   # 慢速移动平均

        # 计算 STC 值
        stc = 100 * (fast_k - slow_d) / slow_d  # 简化的 STC 公式，实际可根据需要调整

        # 根据 STC 判断买入卖出信号
        buy_signal = (stc > 25).astype(int)  # 上穿 25
        sell_signal = (stc < 75).astype(int)  # 下穿 75

        # 最终的信号：买卖信号
        s = buy_signal - sell_signal

        c = d.close

        short_T = 10
        long_T = 300
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        f1 = ma_short / ma_long
        s[f1 < 1.02] = 0
        ######################################################
        price_ratio = d['close'] / d['open']

        is_bullish = price_ratio > 1.0001  # 阳线
        is_bearish = price_ratio < 0.9999  # 阴线

        bullish_count = is_bullish.rolling(window=60).sum()
        bearish_count = is_bearish.rolling(window=60).sum()

        f2 = bullish_count / (bullish_count + bearish_count)
        s[f2 < 0.57] = 0

        return s

    @fill_window(1)
    @staticmethod
    def d_ret_td_signals_chu1(d):
        c = d.close
        td_buy_count = 0
        td_sell_count = 0
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # TD 买入结构
        for i in range(4, len(c)):
            # 买入结构
            if c[i] < c[i - 4]:  # 当前收盘价低于4天前收盘价
                td_buy_count += 1
            else:
                td_buy_count = 0  # 重置计数

            # 满足连续9天买入条件
            if td_buy_count == 9:
                buy_signal[i] = 1  # 产生买入信号
                td_buy_count = 0  # 重置计数器

            # 卖出结构
            if c[i] > c[i - 4]:  # 当前收盘价高于4天前收盘价
                td_sell_count += 1
            else:
                td_sell_count = 0  # 重置计数

            # 满足连续9天卖出条件
            if td_sell_count == 9:
                sell_signal[i] = -1  # 产生卖出信号
                td_sell_count = 0  # 重置计数器

        # 信号：买入信号为 +1，卖出信号为 -1
        ##############################################################
        '''filter with price_position'''
        signals = buy_signal + sell_signal

        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.4)

        signals[f > f_cond] = 0
        ##############################################################

        return signals

    @fill_window(100)
    @staticmethod
    def d_ret_ao_signals_chu1(d):
        c = d.close

        # 计算AO指标
        fast_ma = c.rolling(window=5).mean()  # 快速移动平均线（5周期）
        slow_ma = c.rolling(window=34).mean()  # 慢速移动平均线（34周期）
        ao = fast_ma - slow_ma  # AO = 快速MA - 慢速MA

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # AO由负变正产生买入信号
        for i in range(1, len(ao)):
            if ao[i - 1] < 0 and ao[i] > 0:  # AO由负变正
                buy_signal[i] = 1  # 产生买入信号

            # AO由正变负产生卖出信号
            elif ao[i - 1] > 0 and ao[i] < 0:  # AO由正变负
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal
        ##############################################################
        '''filter with price_position'''
        signals = buy_signal + sell_signal

        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.8)

        signals[f < f_cond] = 0
        ##############################################################

        return signals

    @fill_window(55)
    @staticmethod
    def d_ret_williams_r_sig_price_chu1(d, period=14):
        c = d.close  # 获取收盘价

        # 计算威廉指标（Williams %R）
        highest_high = d.high.rolling(window=period).max()  # 过去14天的最高价
        lowest_low = d.low.rolling(window=period).min()     # 过去14天的最低价
        williams_r = -100 * (highest_high - c) / \
            (highest_high - lowest_low)  # Williams %R 计算

        # 买入信号：威廉指标低于-80
        buy_signal = williams_r < -80

        # 卖出信号：威廉指标高于-20
        sell_signal = williams_r > -20

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal.astype(int) - sell_signal.astype(int)
        ##############################################################
        '''filter with price_position'''
        signals = buy_signal + sell_signal

        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.4)

        signals[f > f_cond] = 0
        ##############################################################

        return signals  # 返回信号序列

    @fill_window(55)
    @staticmethod
    def d_ret_williams_r_sig_price_chu2(d, period=14):
        c = d.close  # 获取收盘价

        # 计算威廉指标（Williams %R）
        highest_high = d.high.rolling(window=period).max()  # 过去14天的最高价
        lowest_low = d.low.rolling(window=period).min()     # 过去14天的最低价
        williams_r = -100 * (highest_high - c) / \
            (highest_high - lowest_low)  # Williams %R 计算

        # 买入信号：威廉指标低于-80
        buy_signal = williams_r < -80

        # 卖出信号：威廉指标高于-20
        sell_signal = williams_r > -20

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal.astype(int) - sell_signal.astype(int)
        ##############################################################
        '''filter with price_position'''
        signals = buy_signal + sell_signal

        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.6)

        signals[f < f_cond] = 0
        ##############################################################

        return signals  # 返回信号序列

    @fill_window(55)
    @staticmethod
    def d_ret_williams_r_sig_price_chu3(d, period=14):
        c = d.close  # 获取收盘价

        # 计算威廉指标（Williams %R）
        highest_high = d.high.rolling(window=period).max()  # 过去14天的最高价
        lowest_low = d.low.rolling(window=period).min()     # 过去14天的最低价
        williams_r = -100 * (highest_high - c) / \
            (highest_high - lowest_low)  # Williams %R 计算

        # 买入信号：威廉指标低于-80
        buy_signal = williams_r < -80

        # 卖出信号：威廉指标高于-20
        sell_signal = williams_r > -20

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal.astype(int) - sell_signal.astype(int)
        ##############################################################

        return signals  # 返回信号序列

    @fill_window(85)
    @staticmethod
    def d_ret_momentum_sig_price_chu1(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

        # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

        # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = golden_cross_buy - death_cross_sell
        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.8)

        signals[f < f_cond] = 0
        ##############################################################

        return signals  # 返回信号序列

    @fill_window(85)
    @staticmethod
    def d_ret_momentum_sig_price_chu2(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

        # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

        # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = golden_cross_buy - death_cross_sell
        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.2)

        signals[f > f_cond] = 0
        ##############################################################

        return signals  # 返回信号序列

    @fill_window(85)
    @staticmethod
    def d_ret_kc_strategy_chu1(d):
        c = d.close
        high = d.high
        low = d.low

        # 设置参数
        kc_period = 20  # 均线计算周期
        atr_period = 14  # ATR计算周期
        atr_multiplier = 2  # ATR倍数

        # 计算20日均线
        ma = c.rolling(window=kc_period).mean()

        # 计算ATR
        tr1 = high - low
        tr2 = (high - c.shift(1)).abs()
        tr3 = (low - c.shift(1)).abs()
        true_range = tr1.combine(tr2, max).combine(tr3, max)
        atr = true_range.rolling(window=atr_period).mean()

        # 计算Keltner Channel上下轨
        kc_upper = ma + (atr * atr_multiplier)  # KC上轨
        kc_lower = ma - (atr * atr_multiplier)  # KC下轨

        # 生成买入和卖出信号
        buy_signal = (c < kc_lower).astype(int)  # 当价格跌破KC下轨时买入
        sell_signal = (c > kc_upper).astype(int)  # 当价格突破KC上轨时卖出

    # 合并信号：1为买入，-1为卖出，0为中性
        signals = buy_signal - sell_signal
        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.2)

        signals[f > f_cond] = 0
        ##############################################################

        return signals  # 返回信号序列

    @fill_window(85)
    @staticmethod
    def d_ret_kc_strategy_chu2(d):
        c = d.close
        high = d.high
        low = d.low

        # 设置参数
        kc_period = 20  # 均线计算周期
        atr_period = 14  # ATR计算周期
        atr_multiplier = 2  # ATR倍数

        # 计算20日均线
        ma = c.rolling(window=kc_period).mean()

        # 计算ATR
        tr1 = high - low
        tr2 = (high - c.shift(1)).abs()
        tr3 = (low - c.shift(1)).abs()
        true_range = tr1.combine(tr2, max).combine(tr3, max)
        atr = true_range.rolling(window=atr_period).mean()

        # 计算Keltner Channel上下轨
        kc_upper = ma + (atr * atr_multiplier)  # KC上轨
        kc_lower = ma - (atr * atr_multiplier)  # KC下轨

        # 生成买入和卖出信号
        buy_signal = (c < kc_lower).astype(int)  # 当价格跌破KC下轨时买入
        sell_signal = (c > kc_upper).astype(int)  # 当价格突破KC上轨时卖出

    # 合并信号：1为买入，-1为卖出，0为中性
        signals = buy_signal - sell_signal
        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.6)

        signals[f < f_cond] = 0
        ##############################################################

        return signals  # 返回信号序列

    @fill_window(85)
    @staticmethod
    def d_ret_bollinger_rsi_signals_chu1(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # 布林带和 RSI 的标准计算周期
        rsi_period = 14  # RSI 的计算周期

        # 计算 RSI
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 计算布林带
        rolling_mean = c.rolling(window=period).mean()
        rolling_std = c.rolling(window=period).std()
        lower_band = rolling_mean - (rolling_std * 2)  # 下轨
        upper_band = rolling_mean + (rolling_std * 2)  # 上轨

        # 买入信号：价格触及布林带下轨且 RSI 低于 30（超卖）
        buy_signal = ((c <= lower_band) & (rsi < 30)).astype(int)

        # 卖出信号：价格触及布林带上轨且 RSI 高于 70（超买）
        sell_signal = ((c >= upper_band) & (rsi > 70)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal - sell_signal

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.6)

        signals[f < f_cond] = 0
        ##############################################################

        return signals

    @fill_window(85)
    @staticmethod
    def d_ret_bollinger_rsi_signals_chu2(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # 布林带和 RSI 的标准计算周期
        rsi_period = 14  # RSI 的计算周期

        # 计算 RSI
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 计算布林带
        rolling_mean = c.rolling(window=period).mean()
        rolling_std = c.rolling(window=period).std()
        lower_band = rolling_mean - (rolling_std * 2)  # 下轨
        upper_band = rolling_mean + (rolling_std * 2)  # 上轨

        # 买入信号：价格触及布林带下轨且 RSI 低于 30（超卖）
        buy_signal = ((c <= lower_band) & (rsi < 30)).astype(int)

        # 卖出信号：价格触及布林带上轨且 RSI 高于 70（超买）
        sell_signal = ((c >= upper_band) & (rsi > 70)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal - sell_signal
        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.6)

        signals[f < f_cond] = 0
        ##############################################################

        return signals

    @fill_window(110)
    @staticmethod
    def d_ret_ma120_macd_1_cross_sig_price_chu1(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算MACD
        def compute_macd(c, short_window=12, long_window=26, signal_window=9):
            exp1 = c.ewm(span=short_window, adjust=False).mean()
            exp2 = c.ewm(span=long_window, adjust=False).mean()
            macd = exp1 - exp2
            signal = macd.ewm(span=signal_window, adjust=False).mean()
            macd_hist = macd - signal
            return macd_hist

        hist = compute_macd(c)
        # 创建买入信号
        condu = ((c > ma120) & (hist.shift(1) < 0) & (hist > 0)).astype(int)
        # 创建卖出信号
        condd = ((c < ma120) & (hist.shift(1) > 0) & (hist < 0)).astype(int)

        s = condu - condd

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.8)

        s[f < f_cond] = 0
        ##############################################################

        return s

    @fill_window(40)
    @staticmethod
    def d_ret_ma120_bolling_cross_sig_price_chu1(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                                                    lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                                                    upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.2)

        s[f > f_cond] = 0
        ##############################################################

        return s

    @fill_window(40)
    @staticmethod
    def d_ret_ma120_bolling_cross_sig_price_chu2(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                                                    lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                                                    upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.2)

        s[f > f_cond] = 0
        ##############################################################

        return s

    @fill_window(85)
    @staticmethod
    def d_ret_macd_02_cross_sig_price_chu1(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.4)

        s[f > f_cond] = 0
        ##############################################################

        return s

    @fill_window(85)
    @staticmethod
    def d_ret_ma120_macd_02_cross_sig_price_chu1(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 创建买入信号
        condu = ((c > ma120) & (macd > 0) & (macd > signal) &
                 (macd.shift(1) <= signal.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (macd < 0) & (macd < signal) &
                 (macd.shift(1) >= signal.shift(1))).astype(int)

        s = condu - condd

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.6)

        s[f < f_cond] = 0
        ##############################################################

        return s

    @fill_window(85)
    @staticmethod
    def d_ret_dc_bbi_cross_sig_price_chu1(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.8)

        s[f < f_cond] = 0
        ##############################################################

        return s

    @fill_window(85)
    @staticmethod
    def d_ret_ma_cci_sig_chu1(d):
        """
        Generates trading signals based on the MA level and CCI threshold:
        - Long entry if MA is above 50 and CCI > 100.
        - Short entry if MA is below 50 and CCI < -100.
        """
        close = d.close
        high = d.high
        low = d.low

        # Parameters
        ma_period = 50  # Moving Average period for trend detection
        cci_period = 20  # CCI period (commonly used)
        cci_threshold = 100  # Threshold for CCI

        # Calculate MA
        ma = close.rolling(window=ma_period).mean()

        # Calculate CCI
        tp = (high + low + close) / 3  # Typical Price for CCI calculation
        cci = (tp - tp.rolling(window=cci_period).mean()) / \
            (0.015 * tp.rolling(window=cci_period).std())

        # Generate Signals
        long_entry = ((ma > 50) & (cci > cci_threshold)).astype(int)
        short_entry = ((ma < 50) & (cci < -cci_threshold)).astype(int)

        # Signal output: 1 for long, -1 for short
        signals = long_entry - short_entry

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.6)

        signals[f < f_cond] = 0
        ##############################################################

        return signals

    @fill_window(85)
    @staticmethod
    def d_ret_ma_short_long_cross_sig_price_chu1(d):
        c = d.close
        long_T1 = 30  # 长期平均线1
        long_T2 = 60  # 长期平均线2
        short_T = 5   # 短期平均线

        # 计算短期和长期移动平均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long1 = c.rolling(window=long_T1).mean()
        ma_long2 = c.rolling(window=long_T2).mean()

        # 确保长期移动平均线大于50
        ma_condition = (ma_long1 > 50) & (ma_long2 > 50)

        # 判断短期平均线是否上穿长期平均线
        crossover_signal1 = (ma_short > ma_long1) & (
            ma_short.shift(1) <= ma_long1.shift(1))
        crossover_signal2 = (ma_short > ma_long2) & (
            ma_short.shift(1) <= ma_long2.shift(1))

        # 结合条件，产生最终信号
        signal = ma_condition.astype(
            int) * (crossover_signal1 | crossover_signal2).astype(int)

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.6)

        signal[f < f_cond] = 0
        ##############################################################

        return signal

    @fill_window(20)
    @staticmethod
    def d_ret_ma_atr_cross_sig_price_chu1(d):
        c = d.close
        high = d.high
        low = d.low
        long_T = 30
        short_T = 5
        atr_T = 14  # Common period for ATR calculation

        # Moving Averages
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # ATR Calculation
        tr = pd.DataFrame()
        tr['h-l'] = high - low
        tr['h-c'] = (high - c.shift()).abs()
        tr['l-c'] = (low - c.shift()).abs()
        atr = tr.max(axis=1).rolling(window=atr_T).mean()

        # Signal conditions based on MA and ATR
        condu = (((ma_short > ma_long).astype(int).diff() == 1).astype(int) +
                 (atr > atr.rolling(window=long_T).mean()).astype(int)) == 2

        condd = (((ma_short < ma_long).astype(int).diff() == 1).astype(int) +
                 (atr < atr.rolling(window=long_T).mean()).astype(int)) == 2

        s = condu.astype(int) - condd.astype(int)

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.4)

        s[f > f_cond] = 0
        ##############################################################

        return s

    @fill_window(55)
    @staticmethod
    def d_ret_ma_atr_cross_sig_price_chu2(d):
        c = d.close
        high = d.high
        low = d.low
        long_T = 30
        short_T = 5
        atr_T = 14  # Common period for ATR calculation

        # Moving Averages
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # ATR Calculation
        tr = pd.DataFrame()
        tr['h-l'] = high - low
        tr['h-c'] = (high - c.shift()).abs()
        tr['l-c'] = (low - c.shift()).abs()
        atr = tr.max(axis=1).rolling(window=atr_T).mean()

        # Signal conditions based on MA and ATR
        condu = (((ma_short > ma_long).astype(int).diff() == 1).astype(int) +
                 (atr > atr.rolling(window=long_T).mean()).astype(int)) == 2

        condd = (((ma_short < ma_long).astype(int).diff() == 1).astype(int) +
                 (atr < atr.rolling(window=long_T).mean()).astype(int)) == 2

        s = condu.astype(int) - condd.astype(int)

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.6)

        s[f < f_cond] = 0
        ##############################################################

        return s

    @fill_window(85)
    @staticmethod
    def d_ret_dpo_ma_cross_sig_price_chu1(d):
        c = d.close
        long_T = 30  # 长期移动平均的周期
        short_T = 5  # 短期DPO的周期
        ma_T = 10  # 用来判断趋势的移动平均周期

        # 计算 DPO 值：当前价格 - 长期移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        dpo = c - ma_short  # DPO是当前价格与短期移动平均的差值

        # 计算MA值来判断趋势方向
        ma = c.rolling(window=ma_T).mean()  # 用ma_T来计算一个中期的简单移动平均

        # 买卖信号：
        # 1. DPO值为正并且价格高于MA时，认为是买入信号
        # 2. DPO值为负并且价格低于MA时，认为是卖出信号
        buy_signal = (dpo > 0) & (c > ma)
        sell_signal = (dpo < 0) & (c < ma)

        # 输出信号，买入信号为1，卖出信号为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.8)

        s[f < f_cond] = 0
        ##############################################################

        return s

    @fill_window(55)
    @staticmethod
    def d_ret_po_signals_chu1(d, short_period=9, long_period=26):
        c = d.close

        # 计算短期和长期 EMA
        ema_short = c.ewm(span=short_period, adjust=False).mean()
        ema_long = c.ewm(span=long_period, adjust=False).mean()

        # 计算 PO
        po = (ema_short - ema_long) / ema_long * 100

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(po)):
            # PO 上穿 0 时，产生买入信号
            if po[i - 1] <= 0 and po[i] > 0:
                buy_signal[i] = 1  # 买入信号

            # PO 下穿 0 时，产生卖出信号
            elif po[i - 1] >= 0 and po[i] < 0:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.6)

        signals[f < f_cond] = 0
        ##############################################################

        return signals

    @fill_window(40)
    @staticmethod
    def d_ret_rma_cross_sig_price_chu1(d):
        """
        基于 RMA 指标的多空信号策略
        :param d: DataFrame 包含 'close', 'high', 'low', 'volume' 列
        :return: 信号序列，多头为 1，空头为 -1，无信号为 0
        """
        c = d.close
        long_T = 30  # 长期周期
        short_T = 5  # 短期周期

        # 定义 RMA 计算公式
        def rma(series, window):
            return series.ewm(alpha=1/window, adjust=False).mean()

        # 计算短期和长期 RMA
        rma_short = rma(c, short_T)
        rma_long = rma(c, long_T)

        # 计算高点和低点的 RMA
        high_short = rma(d.high, short_T)
        high_long = rma(d.high, long_T)
        low_short = rma(d.low, short_T)
        low_long = rma(d.low, long_T)

        # 计算短期和长期的成交量 RMA
        vol_short = rma(d.volume, short_T)
        vol_long = rma(d.volume, long_T)

        # 多头信号条件：短期 RMA 上穿长期 RMA 且短期高点高于长期高点
        condu = ((((rma_short > rma_long).astype(int).diff() == 1).astype(int) +
                  (high_short >= high_long).astype(int)) == 2).astype(int)

        # 空头信号条件：短期 RMA 下穿长期 RMA 且短期低点低于长期低点
        condd = ((((rma_short < rma_long).astype(int).diff() == 1).astype(int) +
                  (low_short <= low_long).astype(int)) == 2).astype(int)

        # 计算成交量的比率
        ft = vol_short / vol_long

        # 返回信号（多头为 1，空头为 -1，无信号为 0）
        s = condu - condd
        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.4)

        s[f > f_cond] = 0
        ##############################################################

        return s

    @fill_window(30)
    @staticmethod
    def d_ret_rsi_boll_sig_chu1(d):
        c = d.close
        high = d.high
        low = d.low

        # Parameters for Bollinger Bands and RSI
        rsi_period = 14
        boll_period = 20
        boll_std = 2

        # Calculate the RSI (Relative Strength Index)
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Calculate Bollinger Bands
        rolling_mean = c.rolling(window=boll_period).mean()
        rolling_std = c.rolling(window=boll_period).std()
        boll_upper = rolling_mean + (boll_std * rolling_std)
        boll_lower = rolling_mean - (boll_std * rolling_std)
        boll_middle = rolling_mean

        # Buy signal condition: RSI crosses above 50 and price is above the middle Bollinger Band
        buy_signal = ((rsi.shift(1) <= 50) & (rsi > 50) & (c > boll_middle))

        # Sell signal condition: Price breaks below the middle Bollinger Band and reaches the lower Bollinger Band
        sell_signal = ((c.shift(1) > boll_middle) & (
            c < boll_middle) & (c <= boll_lower))

        # Signal output: 1 for buy, -1 for sell, 0 for no action
        s = buy_signal.astype(int) - sell_signal.astype(int)

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.6)

        s[f < f_cond] = 0
        ##############################################################

        return s

    @fill_window(85)
    @staticmethod
    def d_ret_stc_sig_price_chu1(d):
        c = d.close
        long_T = 30
        short_T = 5

        # 计算短期和长期的均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算短期和长期的最高价与最低价
        high_short = c.rolling(window=short_T).max()
        high_long = c.rolling(window=long_T).max()
        low_short = c.rolling(window=short_T).min()
        low_long = c.rolling(window=long_T).min()

        # 计算成交量的短期和长期和
        vol_short = c.rolling(window=short_T).sum()
        vol_long = c.rolling(window=long_T).sum()

        # 计算上穿下穿的条件
        condu = ((((ma_short > ma_long).astype(int).diff() == 1).astype(
            int) + (high_short >= high_long).astype(int)) == 2).astype(int)
        condd = ((((ma_short < ma_long).astype(int).diff() == 1).astype(
            int) + (low_short <= low_long).astype(int)) == 2).astype(int)

        # 计算成交量的相对值
        ft = vol_short / vol_long

        # 计算买入卖出信号的基础信号
        s = condu - condd

        # --- STC Indicator Calculation ---

        # 计算快速和慢速移动平均线
        fast_k = c.rolling(window=short_T).mean()  # 快速移动平均
        slow_d = c.rolling(window=long_T).mean()   # 慢速移动平均

        # 计算 STC 值
        stc = 100 * (fast_k - slow_d) / slow_d  # 简化的 STC 公式，实际可根据需要调整

        # 根据 STC 判断买入卖出信号
        buy_signal = (stc > 25).astype(int)  # 上穿 25
        sell_signal = (stc < 75).astype(int)  # 下穿 75

        # 最终的信号：买卖信号
        s = buy_signal - sell_signal

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.6)

        s[f < f_cond] = 0
        ##############################################################

        return s

    @fill_window(85)
    @staticmethod
    def d_ret_stc_sig_price_chu2(d):
        c = d.close
        long_T = 30
        short_T = 5

        # 计算短期和长期的均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算短期和长期的最高价与最低价
        high_short = c.rolling(window=short_T).max()
        high_long = c.rolling(window=long_T).max()
        low_short = c.rolling(window=short_T).min()
        low_long = c.rolling(window=long_T).min()

        # 计算成交量的短期和长期和
        vol_short = c.rolling(window=short_T).sum()
        vol_long = c.rolling(window=long_T).sum()

        # 计算上穿下穿的条件
        condu = ((((ma_short > ma_long).astype(int).diff() == 1).astype(
            int) + (high_short >= high_long).astype(int)) == 2).astype(int)
        condd = ((((ma_short < ma_long).astype(int).diff() == 1).astype(
            int) + (low_short <= low_long).astype(int)) == 2).astype(int)

        # 计算成交量的相对值
        ft = vol_short / vol_long

        # 计算买入卖出信号的基础信号
        s = condu - condd

        # --- STC Indicator Calculation ---

        # 计算快速和慢速移动平均线
        fast_k = c.rolling(window=short_T).mean()  # 快速移动平均
        slow_d = c.rolling(window=long_T).mean()   # 慢速移动平均

        # 计算 STC 值
        stc = 100 * (fast_k - slow_d) / slow_d  # 简化的 STC 公式，实际可根据需要调整

        # 根据 STC 判断买入卖出信号
        buy_signal = (stc > 25).astype(int)  # 上穿 25
        sell_signal = (stc < 75).astype(int)  # 下穿 75

        # 最终的信号：买卖信号
        s = buy_signal - sell_signal

        ##############################################################
        '''filter with price_position'''
        up = d['high'].rolling(20).max()
        down = d['low'].rolling(20).min()
        f = (d['close'] - down) / (up - down)

        f_cond = f.expanding().quantile(0.4)

        s[f > f_cond] = 0
        ##############################################################

        return s

    @staticmethod
    def ret_hv_ratio_signals(d, short_period=10, long_period=30, threshold_high=1.5, threshold_low=0.5):
        c = d.close

        # 计算每日收益率的对数
        log_returns = np.log(c / c.shift(1))

        # 计算短期HV
        hv_short = log_returns.rolling(
            window=short_period).std() * np.sqrt(252)  # 年化波动率

        # 计算长期HV
        hv_long = log_returns.rolling(
            window=long_period).std() * np.sqrt(252)  # 年化波动率

        # 计算HV比率
        hv_ratio = hv_short / hv_long

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(len(hv_ratio)):
            # HV比率低于低阈值时，产生买入信号
            if hv_ratio[i] < threshold_low:
                buy_signal[i] = 1  # 买入信号

            # HV比率高于高阈值时，产生卖出信号
            elif hv_ratio[i] > threshold_high:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_td_signals(d):
        c = d.close
        td_buy_count = 0
        td_sell_count = 0
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # TD 买入结构
        for i in range(4, len(c)):
            # 买入结构
            if c[i] < c[i - 4]:  # 当前收盘价低于4天前收盘价
                td_buy_count += 1
            else:
                td_buy_count = 0  # 重置计数

            # 满足连续9天买入条件
            if td_buy_count == 9:
                buy_signal[i] = 1  # 产生买入信号
                td_buy_count = 0  # 重置计数器

            # 卖出结构
            if c[i] > c[i - 4]:  # 当前收盘价高于4天前收盘价
                td_sell_count += 1
            else:
                td_sell_count = 0  # 重置计数

            # 满足连续9天卖出条件
            if td_sell_count == 9:
                sell_signal[i] = -1  # 产生卖出信号
                td_sell_count = 0  # 重置计数器

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_ao_signals(d):
        c = d.close

        # 计算AO指标
        fast_ma = c.rolling(window=5).mean()  # 快速移动平均线（5周期）
        slow_ma = c.rolling(window=34).mean()  # 慢速移动平均线（34周期）
        ao = fast_ma - slow_ma  # AO = 快速MA - 慢速MA

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # AO由负变正产生买入信号
        for i in range(1, len(ao)):
            if ao[i - 1] < 0 and ao[i] > 0:  # AO由负变正
                buy_signal[i] = 1  # 产生买入信号

            # AO由正变负产生卖出信号
            elif ao[i - 1] > 0 and ao[i] < 0:  # AO由正变负
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_ena_signals(d):
        c = d.close
        # 计算指数加权移动平均（EMA）
        ema_period = 14
        ema = c.ewm(span=ema_period, adjust=False).mean()

        # 归一化ENA指标
        max_ema = ema.max()  # 计算EMA的最大值
        min_ema = ema.min()  # 计算EMA的最小值
        ena = (ema - min_ema) / (max_ema - min_ema)  # 归一化ENA

        # 计算ENA的信号线（EMA的移动平均）
        ena_signal = ena.ewm(span=9, adjust=False).mean()  # 9周期的EMA作为信号线

        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        # ENA由下向上穿越信号线时产生买入信号
        for i in range(1, len(ena)):
            if ena[i - 1] < ena_signal[i - 1] and ena[i] > ena_signal[i]:  # ENA由下向上穿越信号线
                buy_signal[i] = 1  # 产生买入信号

            # ENA由上向下穿越信号线时产生卖出信号
            elif ena[i - 1] > ena_signal[i - 1] and ena[i] < ena_signal[i]:  # ENA由上向下穿越信号线
                sell_signal[i] = -1  # 产生卖出信号

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_williams_r_sig_price(d, period=14):
        c = d.close  # 获取收盘价

        # 计算威廉指标（Williams %R）
        highest_high = d.high.rolling(window=period).max()  # 过去14天的最高价
        lowest_low = d.low.rolling(window=period).min()     # 过去14天的最低价
        williams_r = -100 * (highest_high - c) / \
            (highest_high - lowest_low)  # Williams %R 计算

        # 买入信号：威廉指标低于-80
        buy_signal = williams_r < -80

        # 卖出信号：威廉指标高于-20
        sell_signal = williams_r > -20

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal.astype(int) - sell_signal.astype(int)

        return signals  # 返回信号序列

    @staticmethod
    def ret_momentum_sig_price(d, momentum_period=14):
        c = d.close  # 获取收盘价
        # 计算动量值
        momentum = c.diff(periods=momentum_period)  # 动量值 = 当前收盘价 - 过去指定周期的收盘价

        # 买入信号：动量值大于0并上升
        golden_cross_buy = ((momentum > 0) & (
            momentum > momentum.shift(1))).astype(int)

        # 卖出信号：动量值小于0并下降
        death_cross_sell = ((momentum < 0) & (
            momentum < momentum.shift(1))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = golden_cross_buy - death_cross_sell

        return signals  # 返回信号序列

    @staticmethod
    def ret_kc_strategy(d):
        c = d.close
        high = d.high
        low = d.low

        # 设置参数
        kc_period = 20  # 均线计算周期
        atr_period = 14  # ATR计算周期
        atr_multiplier = 2  # ATR倍数

        # 计算20日均线
        ma = c.rolling(window=kc_period).mean()

        # 计算ATR
        tr1 = high - low
        tr2 = (high - c.shift(1)).abs()
        tr3 = (low - c.shift(1)).abs()
        true_range = tr1.combine(tr2, max).combine(tr3, max)
        atr = true_range.rolling(window=atr_period).mean()

        # 计算Keltner Channel上下轨
        kc_upper = ma + (atr * atr_multiplier)  # KC上轨
        kc_lower = ma - (atr * atr_multiplier)  # KC下轨

        # 生成买入和卖出信号
        buy_signal = (c < kc_lower).astype(int)  # 当价格跌破KC下轨时买入
        sell_signal = (c > kc_upper).astype(int)  # 当价格突破KC上轨时卖出

        # 合并信号：1为买入，-1为卖出，0为中性
        signals = buy_signal - sell_signal

        return signals

    @staticmethod
    def ret_bollinger_rsi_signals(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # 布林带和 RSI 的标准计算周期
        rsi_period = 14  # RSI 的计算周期

        # 计算 RSI
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # 计算布林带
        rolling_mean = c.rolling(window=period).mean()
        rolling_std = c.rolling(window=period).std()
        lower_band = rolling_mean - (rolling_std * 2)  # 下轨
        upper_band = rolling_mean + (rolling_std * 2)  # 上轨

        # 买入信号：价格触及布林带下轨且 RSI 低于 30（超卖）
        buy_signal = ((c <= lower_band) & (rsi < 30)).astype(int)

        # 卖出信号：价格触及布林带上轨且 RSI 高于 70（超买）
        sell_signal = ((c >= upper_band) & (rsi > 70)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal - sell_signal

        return signals

    @staticmethod
    def ret_macd_sig_price(d):
        c = d.close
        period_short = 12  # 快速EMA的周期
        period_long = 26   # 慢速EMA的周期
        signal_period = 9  # 信号线的周期

        # 计算快速和慢速EMA
        ema_short = c.ewm(span=period_short, adjust=False).mean()
        ema_long = c.ewm(span=period_long, adjust=False).mean()

        # 计算 MACD 线
        macd_line = ema_short - ema_long

        # 计算信号线
        signal_line = macd_line.ewm(span=signal_period, adjust=False).mean()

        # 买入信号：MACD 线向上穿越信号线
        golden_cross_buy = ((macd_line.shift(1) < signal_line.shift(1)) & (
            macd_line >= signal_line)).astype(int)

        # 卖出信号：MACD 线向下穿越信号线
        death_cross_sell = ((macd_line.shift(1) > signal_line.shift(1)) & (
            macd_line <= signal_line)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = golden_cross_buy - death_cross_sell

        return signals

    @staticmethod
    def ret_ma_arrangement_sig(d):
        close = d.close  # 收盘价
        low = d.low      # K线最低价

        # 计算均线
        ma5 = close.rolling(window=5).mean()
        ma10 = close.rolling(window=10).mean()
        ma20 = close.rolling(window=20).mean()

        # 买入信号：第一次出现币价 > MA5 > MA10 > MA20
        buy_signal = ((low > ma5) & (ma5 > ma10) & (ma10 > ma20) &
                      (~((low.shift(1) > ma5.shift(1)) & (ma5.shift(1) > ma10.shift(1)) & (ma10.shift(1) > ma20.shift(1))))).astype(int)

        # 卖出信号：第一次出现币价 < MA5 < MA10 < MA20
        sell_signal = ((low < ma5) & (ma5 < ma10) & (ma10 < ma20) &
                       (~((low.shift(1) < ma5.shift(1)) & (ma5.shift(1) < ma10.shift(1)) & (ma10.shift(1) < ma20.shift(1))))).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal - sell_signal

        return signals

    @staticmethod
    def ret_ma20_ma120_cross_sig_price(d):

        c = d.close

        # 计算 MA20 和 MA120
        ma20 = c.rolling(window=20).mean()
        ma120 = c.rolling(window=120).mean()

        # 创建买入信号
        condu = ((c > ma120) & (c > ma20) & (c.shift(1) > ma120)
                 & (c.shift(1) <= ma20)).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (c < ma20) & (c.shift(1) < ma120)
                 & (c.shift(1) >= ma20)).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_rsi_ma120_cross_sig_price(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 RSI
        def compute_rsi(s, window=14):

            delta = c.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            return rsi

        rsi = compute_rsi(c)
        # 创建买入信号
        condu = ((c > ma120) & (rsi < 20) & (c > c.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (rsi > 80) & (c < c.shift(1))).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_ma120_macd_1_cross_sig_price(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算MACD
        def compute_macd(c, short_window=12, long_window=26, signal_window=9):
            exp1 = c.ewm(span=short_window, adjust=False).mean()
            exp2 = c.ewm(span=long_window, adjust=False).mean()
            macd = exp1 - exp2
            signal = macd.ewm(span=signal_window, adjust=False).mean()
            macd_hist = macd - signal
            return macd_hist

        hist = compute_macd(c)
        # 创建买入信号
        condu = ((c > ma120) & (hist.shift(1) < 0) & (hist > 0)).astype(int)
        # 创建卖出信号
        condd = ((c < ma120) & (hist.shift(1) > 0) & (hist < 0)).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_ma120_bolling_cross_sig_price(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算机布林带
        def compute_bollinger_bands(c, window=20, num_std_dev=2):

            middle_band = c.rolling(window).mean()
            upper_band = middle_band + (c.rolling(window).std() * num_std_dev)
            lower_band = middle_band - (c.rolling(window).std() * num_std_dev)

            return middle_band, upper_band, lower_band

        middle_band, upper_band, lower_band = compute_bollinger_bands(c)

        # 创建买入信号
        # condu1 = ((c > ma120) & (c.shift(1) <= middle_band.shift(1)) & (c > middle_band)).astype(int)
        # 创建加仓买入信号
        condu = ((c > ma120) & (c < middle_band) & (c.shift(1) >=
                 lower_band.shift(1)) & (c > lower_band)).astype(int)

        # 创建卖出信号
        # condd1 = ((c < ma120) & (c.shift(1) >= middle_band.shift(1)) & (c < middle_band)).astype(int)
        # 创建加仓买入信号
        condd = ((c < ma120) & (c > middle_band) & (c.shift(1) <=
                 upper_band.shift(1)) & (c < upper_band)).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_ma120_cci_cross_sig_price(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 CCI
        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        cci_20 = compute_rsi(d, 20)

        # 创建买入信号
        condu = ((c > ma120) & (cci_20 < -100) & (c > c.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (cci_20 > 100) & (c < c.shift(1))).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_macd_02_cross_sig_price(d):

        c = d.close

        # 计算 EMA
        def ema(series, span):
            return series.ewm(span=span, adjust=False).mean()

        # 计算 MACD
        ema12 = ema(c, 12)
        ema26 = ema(c, 26)
        macd = ema12 - ema26
        signal = ema(macd, 9)

        # 创建买入信号
        condu = ((macd > 0) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((macd < 0) & (macd < signal)).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_ma120_macd_02_cross_sig_price(d):

        c = d.close

        # 计算 MA120
        ma120 = c.rolling(window=120).mean()

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 创建买入信号
        condu = ((c > ma120) & (macd > 0) & (macd > signal) &
                 (macd.shift(1) <= signal.shift(1))).astype(int)

        # 创建卖出信号
        condd = ((c < ma120) & (macd < 0) & (macd < signal) &
                 (macd.shift(1) >= signal.shift(1))).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def ret_cci_fibonacci_signals(d):
        c = d.close
        high = d.high
        low = d.low

        # 计算CCI（Commodity Channel Index）
        period = 14
        typical_price = (high + low + c) / 3  # 典型价格
        sma = typical_price.rolling(window=period).mean()  # 典型价格的简单移动平均
        mean_deviation = (
            typical_price - sma).abs().rolling(window=period).mean()  # 平均偏差
        cci = (typical_price - sma) / (0.015 * mean_deviation)  # CCI公式

        # 计算斐波那契回撤水平
        fib_levels = [0.236, 0.382, 0.5, 0.618]  # 常见的斐波那契回撤水平
        fib_retracements = pd.DataFrame(index=c.index)

        # 计算每个周期的高低价差
        high_max = high.rolling(window=period).max()
        low_min = low.rolling(window=period).min()

        # 计算斐波那契回撤位
        fib_retracements['fib_23_6'] = high_max - \
            (high_max - low_min) * fib_levels[0]
        fib_retracements['fib_38_2'] = high_max - \
            (high_max - low_min) * fib_levels[1]
        fib_retracements['fib_50_0'] = high_max - \
            (high_max - low_min) * fib_levels[2]
        fib_retracements['fib_61_8'] = high_max - \
            (high_max - low_min) * fib_levels[3]

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(c)):
            # 如果CCI大于100且当前价格接近斐波那契回撤23.6%或者38.2%
            if cci[i] > 100 and (abs(c[i] - fib_retracements['fib_23_6'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                 abs(c[i] - fib_retracements['fib_38_2'][i]) < 0.02 * (high_max[i] - low_min[i])):
                sell_signal[i] = -1  # 卖出信号

            # 如果CCI小于-100且当前价格接近斐波那契回撤61.8%或者50%
            elif cci[i] < -100 and (abs(c[i] - fib_retracements['fib_61_8'][i]) < 0.02 * (high_max[i] - low_min[i]) or
                                    abs(c[i] - fib_retracements['fib_50_0'][i]) < 0.02 * (high_max[i] - low_min[i])):
                buy_signal[i] = 1  # 买入信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        s = buy_signal + sell_signal

        return s

    @staticmethod
    def ret_ma20_volume_cross_signals(d):
        # 获取收盘价和交易量数据
        c = d.close
        volume = d.volume

        # 计算短期和长期的MA
        short_window = 50  # 短期MA窗口
        long_window = 200  # 长期MA窗口
        MA_short = c.rolling(window=short_window).mean()  # 短期MA
        MA_long = c.rolling(window=long_window).mean()  # 长期MA

        # 计算前300根K线的平均交易量
        avg_volume = volume.rolling(window=300).mean()

        # 初始化信号列，0代表没有信号
        signals = pd.Series(0, index=c.index)

        # 遍历数据生成买卖信号
        for i in range(1, len(c)):
            # 金叉条件：短期MA上穿长期MA且当前为阳线，且当前阳线收盘价的交易量大于前300根K线的平均交易量
            if MA_short.iloc[i] > MA_long.iloc[i] and MA_short.iloc[i-1] <= MA_long.iloc[i-1]:
                if c[i] > c[i-1] and volume[i] > avg_volume.iloc[i]:
                    signals[i] = 1  # 买入信号

        # 死叉条件：短期MA下穿长期MA且当前为阴线，且当前阴线收盘价的交易量大于前300根K线的平均交易量
            elif MA_short.iloc[i] < MA_long.iloc[i] and MA_short.iloc[i-1] >= MA_long.iloc[i-1]:
                if c[i] < c[i-1] and volume[i] > avg_volume.iloc[i]:
                    signals[i] = -1  # 卖出信号

        return signals

    @staticmethod
    def ret_ma20_rsi_macd_cross_sig_price(d):

        c = d.close

        # 计算 CCI
        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        rsi = compute_rsi(d, 20)
        # cci = (c - c.rolling(window=20).mean()) / (0.015 * c.rolling(window=20).std())

        # 计算 MACD
        def compute_macd(c):

            ema12 = c.ewm(span=12, adjust=False).mean()
            ema26 = c.ewm(span=26, adjust=False).mean()
            macd = ema12 - ema26
            signal = macd.ewm(span=9, adjust=False).mean()

            return macd, signal

        macd, signal = compute_macd(c)

        # 计算 MA20 和 MA120
        ma20 = c.rolling(window=20).mean()
        ma120 = c.rolling(window=120).mean()

        # 创建买入信号
        condu = ((rsi < 20) & (ma20 > ma120) & (macd > signal)).astype(int)

        # 创建卖出信号
        condd = ((rsi > 80) & (ma20 < ma120) & (macd < signal)).astype(int)

        s = condu - condd

        return s

    @staticmethod
    def generate_bbi_factor(d):
        c = d['close']

        # BBI计算：BBI是多个周期均线的平均
        ma_3 = c.rolling(window=3).mean()   # 3周期均线
        ma_6 = c.rolling(window=6).mean()   # 6周期均线
        ma_12 = c.rolling(window=12).mean()  # 12周期均线
        ma_24 = c.rolling(window=24).mean()  # 24周期均线

        d['BBI'] = (ma_3 + ma_6 + ma_12 + ma_24) / 4  # 计算BBI线

        # 买入信号条件：价格由下向上突破BBI线
        buy_signal = (c > d['BBI']) & (c.shift(1) <= d['BBI'].shift(1))

        # 卖出信号条件：价格由上向下跌破BBI线
        sell_signal = (c < d['BBI']) & (c.shift(1) >= d['BBI'].shift(1))

        # 初始化信号：0 表示无信号
        s = pd.Series(0, index=d.index)

        # 设置买入和卖出信号
        s[buy_signal] = 1   # 买入信号
        s[sell_signal] = -1  # 卖出信号

        return s

    @staticmethod
    def ret_dc_bbi_cross_sig_price(d):
        c = d.close
        high = d.high
        low = d.low

        # 唐奇安通道参数
        dc_period = 20  # 唐奇安通道的周期
        upper = high.rolling(window=dc_period).max()
        lower = low.rolling(window=dc_period).min()
        middle = (upper + lower) / 2

        # BBI指标参数
        bbi_short = 5
        bbi_medium = 10
        bbi_long = 20
        bbi_very_long = 60

        sma_5 = c.rolling(window=bbi_short).mean()
        sma_10 = c.rolling(window=bbi_medium).mean()
        sma_20 = c.rolling(window=bbi_long).mean()
        sma_60 = c.rolling(window=bbi_very_long).mean()

        bbi = (sma_5 + sma_10 + sma_20 + sma_60) / 4

        # 生成信号
        buy_signal = ((c > middle) & (c > bbi))  # 收盘价在唐奇安通道上轨以上且高于BBI
        sell_signal = ((c < middle) & (c < bbi))  # 收盘价在唐奇安通道下轨以下且低于BBI

        # 返回买卖信号，买为1，卖为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @staticmethod
    def ret_ma_cci_sig(d):
        """
        Generates trading signals based on the MA level and CCI threshold:
        - Long entry if MA is above 50 and CCI > 100.
        - Short entry if MA is below 50 and CCI < -100.
        """
        close = d.close
        high = d.high
        low = d.low

        # Parameters
        ma_period = 50  # Moving Average period for trend detection
        cci_period = 20  # CCI period (commonly used)
        cci_threshold = 100  # Threshold for CCI

        # Calculate MA
        ma = close.rolling(window=ma_period).mean()

        # Calculate CCI
        tp = (high + low + close) / 3  # Typical Price for CCI calculation
        cci = (tp - tp.rolling(window=cci_period).mean()) / \
            (0.015 * tp.rolling(window=cci_period).std())

        # Generate Signals
        long_entry = ((ma > 50) & (cci > cci_threshold)).astype(int)
        short_entry = ((ma < 50) & (cci < -cci_threshold)).astype(int)

        # Signal output: 1 for long, -1 for short
        signals = long_entry - short_entry

        return signals

    @staticmethod
    def ret_ma_vol_cci_sig(d):
        c = d.close
        vol = d.volume
        # cci = d.cci  # 假设数据集d包含CCI指标列

        def compute_rsi(d, window=14):

            typical_price = (d.high + d.low + d.close) / 3
            sma = typical_price.rolling(window=window).mean()
            mad = (typical_price - sma).abs().rolling(window=window).mean()
            cci = (typical_price - sma) / (0.015 * mad)

            return cci

        cci = compute_rsi(d)

        ma_window = 50
        vol_days_threshold = 3  # VOL放大的连续天数
        vol_increase_factor = 1.2  # 设定放大量度，比如比长期均量大20%

        # 计算50日均线
        ma_50 = c.rolling(window=ma_window).mean()

        # 计算VOL短期均量
        vol_short = vol.rolling(window=vol_days_threshold).mean()
        vol_long = vol.rolling(window=ma_window).mean()

        # 条件：价格均线位于50均线上方
        price_above_ma = (c > ma_50).astype(int)

        # 条件：VOL连续几天放大
        vol_increase = ((vol_short > vol_long * vol_increase_factor).astype(int).rolling(
            window=vol_days_threshold).sum() == vol_days_threshold).astype(int)

        # 条件：CCI指标低于-100
        cci_condition = (cci < -100).astype(int)

        # 所有条件都满足则产生买入信号
        signal = (price_above_ma & vol_increase & cci_condition).astype(int)

        return signal

    @staticmethod
    def ret_ma_atr_cross_sig_price(d):
        c = d.close
        high = d.high
        low = d.low
        long_T = 30
        short_T = 5
        atr_T = 14  # Common period for ATR calculation

        # Moving Averages
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # ATR Calculation
        tr = pd.DataFrame()
        tr['h-l'] = high - low
        tr['h-c'] = (high - c.shift()).abs()
        tr['l-c'] = (low - c.shift()).abs()
        atr = tr.max(axis=1).rolling(window=atr_T).mean()

        # Signal conditions based on MA and ATR
        condu = (((ma_short > ma_long).astype(int).diff() == 1).astype(int) +
                 (atr > atr.rolling(window=long_T).mean()).astype(int)) == 2

        condd = (((ma_short < ma_long).astype(int).diff() == 1).astype(int) +
                 (atr < atr.rolling(window=long_T).mean()).astype(int)) == 2

        s = condu.astype(int) - condd.astype(int)

        return s

    @staticmethod
    def ret_dpo_ma_cross_sig_price(d):
        c = d.close
        long_T = 30  # 长期移动平均的周期
        short_T = 5  # 短期DPO的周期
        ma_T = 10  # 用来判断趋势的移动平均周期

        # 计算 DPO 值：当前价格 - 长期移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()
        dpo = c - ma_short  # DPO是当前价格与短期移动平均的差值

        # 计算MA值来判断趋势方向
        ma = c.rolling(window=ma_T).mean()  # 用ma_T来计算一个中期的简单移动平均

        # 买卖信号：
        # 1. DPO值为正并且价格高于MA时，认为是买入信号
        # 2. DPO值为负并且价格低于MA时，认为是卖出信号
        buy_signal = (dpo > 0) & (c > ma)
        sell_signal = (dpo < 0) & (c < ma)

        # 输出信号，买入信号为1，卖出信号为-1，其他为0
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @staticmethod
    def ret_po_signals(d, short_period=9, long_period=26):
        c = d.close

        # 计算短期和长期 EMA
        ema_short = c.ewm(span=short_period, adjust=False).mean()
        ema_long = c.ewm(span=long_period, adjust=False).mean()

        # 计算 PO
        po = (ema_short - ema_long) / ema_long * 100

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(po)):
            # PO 上穿 0 时，产生买入信号
            if po[i - 1] <= 0 and po[i] > 0:
                buy_signal[i] = 1  # 买入信号

            # PO 下穿 0 时，产生卖出信号
            elif po[i - 1] >= 0 and po[i] < 0:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_rma_cross_sig_price(d):
        """
        基于 RMA 指标的多空信号策略
        :param d: DataFrame 包含 'close', 'high', 'low', 'volume' 列
        :return: 信号序列，多头为 1，空头为 -1，无信号为 0
        """
        c = d.close
        long_T = 30  # 长期周期
        short_T = 5  # 短期周期

        # 定义 RMA 计算公式
        def rma(series, window):
            return series.ewm(alpha=1/window, adjust=False).mean()

        # 计算短期和长期 RMA
        rma_short = rma(c, short_T)
        rma_long = rma(c, long_T)

        # 计算高点和低点的 RMA
        high_short = rma(d.high, short_T)
        high_long = rma(d.high, long_T)
        low_short = rma(d.low, short_T)
        low_long = rma(d.low, long_T)

        # 计算短期和长期的成交量 RMA
        vol_short = rma(d.volume, short_T)
        vol_long = rma(d.volume, long_T)

        # 多头信号条件：短期 RMA 上穿长期 RMA 且短期高点高于长期高点
        condu = ((((rma_short > rma_long).astype(int).diff() == 1).astype(int) +
                  (high_short >= high_long).astype(int)) == 2).astype(int)

        # 空头信号条件：短期 RMA 下穿长期 RMA 且短期低点低于长期低点
        condd = ((((rma_short < rma_long).astype(int).diff() == 1).astype(int) +
                  (low_short <= low_long).astype(int)) == 2).astype(int)

        # 计算成交量的比率
        ft = vol_short / vol_long

        # 返回信号（多头为 1，空头为 -1，无信号为 0）
        s = condu - condd
        return s

    @staticmethod
    def ret_ma120_bbi_signals(d):
        c = d['close']  # 收盘价格
        MA120 = c.rolling(window=120).mean()  # 120日移动平均线
        BBI = (c.rolling(window=3).mean() + c.rolling(window=5).mean() +
               c.rolling(window=8).mean()) / 3  # 假设BBI为三条移动平均的均值

        # 做多买入信号：价格大于MA120且由下向上突破BBI线
        buy_signal = ((c > MA120) & (c.shift(1) <= BBI.shift(1))
                      & (c > BBI)).astype(int)

        # 做空卖出信号：价格小于MA120且由下向下跌破BBI线
        sell_signal = ((c < MA120) & (c.shift(1) >= BBI.shift(1))
                       & (c < BBI)).astype(int)

        # 合并信号：1表示买入信号，-1表示卖出信号，0表示没有信号
        signal = buy_signal - sell_signal

        return signal

    @staticmethod
    def ret_skdj_sig_price(d):
        c = d.close
        low = d.low
        high = d.high
        period = 14  # SKDJ的标准计算周期

        # 计算SKDJ的 %K 和 %D 线
        lowest_low = low.rolling(window=period).min()
        highest_high = high.rolling(window=period).max()
        k_line = ((c - lowest_low) / (highest_high - lowest_low)) * 100
        d_line = k_line.rolling(window=3).mean()

        # 金叉买入信号：%K 上穿 %D 且都小于 25
        golden_cross_buy = ((k_line.shift(1) < d_line.shift(1)) & (
            k_line >= d_line) & (k_line < 25) & (d_line < 25)).astype(int)

        # 死叉卖出信号：%K 下穿 %D 且都大于 75
        death_cross_sell = ((k_line.shift(1) > d_line.shift(1)) & (
            k_line <= d_line) & (k_line > 75) & (d_line > 75)).astype(int)

        # 信号：买入信号为 +1，卖出信号为 -1
        s = golden_cross_buy - death_cross_sell

        return s

    @staticmethod
    def ret_vao_signals(d, n1=20, n2=50):
        c = d.close
        h = d.high
        l = d.low
        v = d.volume

        # 计算 WEIGHTED_VOLUME
        weighted_volume = v * (c - (h + l) / 2)

        # 计算 VAO
        vao = weighted_volume.cumsum()  # 累积加和实现 REF(VAO, 1) + WEIGHTED_VOLUME

        # 计算 VAO 的短期和长期均线
        vao_ma1 = vao.rolling(window=n1).mean()
        vao_ma2 = vao.rolling(window=n2).mean()

        # 买卖信号
        buy_signal = pd.Series(0, index=c.index)
        sell_signal = pd.Series(0, index=c.index)

        for i in range(1, len(vao)):
            # 短期均线上穿长期均线时，产生买入信号
            if vao_ma1[i - 1] <= vao_ma2[i - 1] and vao_ma1[i] > vao_ma2[i]:
                buy_signal[i] = 1  # 买入信号

            # 短期均线下穿长期均线时，产生卖出信号
            elif vao_ma1[i - 1] >= vao_ma2[i - 1] and vao_ma1[i] < vao_ma2[i]:
                sell_signal[i] = -1  # 卖出信号

        # 综合信号：买入信号为 +1，卖出信号为 -1
        signals = buy_signal + sell_signal

        return signals

    @staticmethod
    def ret_wma_signals(d):

        def weighted_moving_average(series, window):
            weights = range(1, window + 1)  # 权重从1到window
            return series.rolling(window=window).apply(lambda x: (x * weights).sum() / sum(weights), raw=True)

        c = d['close']  # 收盘价格
        short_T = 5  # 短期WMA窗口
        long_T = 30  # 长期WMA窗口

        # 计算短期和长期的WMA
        wma_short = weighted_moving_average(c, short_T)
        wma_long = weighted_moving_average(c, long_T)

        # 做多买入信号：短期WMA突破长期WMA
        buy_signal = ((wma_short > wma_long) & (
            wma_short.shift(1) <= wma_long.shift(1))).astype(int)

        # 做空卖出信号：短期WMA跌破长期WMA
        sell_signal = ((wma_short < wma_long) & (
            wma_short.shift(1) >= wma_long.shift(1))).astype(int)

        # 最终信号：1为买入信号，-1为卖出信号，0为无信号
        signal = buy_signal - sell_signal

        return signal

    @staticmethod
    def ret_rsi_bb_ma_signal(d):

        def calculate_rsi(series, period=14):
            delta = series.diff()
            gain = delta.where(delta > 0, 0).rolling(window=period).mean()
            loss = -delta.where(delta < 0, 0).rolling(window=period).mean()
            rs = gain / loss
            return 100 - (100 / (1 + rs))

        c = d.close
        rsi = calculate_rsi(c)
        mid_band = c.rolling(20).mean()
        upper_band = mid_band + 2 * c.rolling(20).std()
        lower_band = mid_band - 2 * c.rolling(20).std()
        ma120 = c.rolling(120).mean()

        buy_signal = ((rsi > 50) & (c > mid_band) & (
            c.shift(1) <= mid_band)).astype(int)
        add_position_signal = ((buy_signal.cumsum() > 0) & (
            c < mid_band) & (c > lower_band)).astype(int)
        sell_signal = (c >= upper_band).astype(int)

        signals = buy_signal - sell_signal + add_position_signal
        signals[ma120 > c] *= -1  # Reverse signals if below MA120
        return signals

    @staticmethod
    def ret_macd_cross_signal(d):
        c = d.close  # Get closing prices
        short_T = 12  # Short-term EMA period
        long_T = 26   # Long-term EMA period
        signal_T = 9  # Signal line period (MACD signal line)

        # Calculate short-term and long-term EMAs
        ema_short = c.ewm(span=short_T, adjust=False).mean()
        ema_long = c.ewm(span=long_T, adjust=False).mean()

        # Calculate DIF (Difference between short-term and long-term EMAs)
        dif = ema_short - ema_long

        # Calculate MACD (Signal line is an EMA of the DIF)
        macd = dif.ewm(span=signal_T, adjust=False).mean()

        # Calculate the difference between closing price and MACD for bar height
        bar = c - macd

        # Detect Golden Cross (DIF crosses above MACD)
        golden_cross = ((dif > macd) & (
            dif.shift(1) <= macd.shift(1))).astype(int)

        # Detect Death Cross (DIF crosses below MACD)
        death_cross = ((dif < macd) & (
            dif.shift(1) >= macd.shift(1))).astype(int)

        # Detect shrinking bars (current bar is smaller than the previous one)
        bar_shrink = (bar.abs() < bar.shift(1).abs()).astype(int)

        # Buy signal: Golden cross with shrinking green bars
        buy_signal = golden_cross & bar_shrink

        # Sell signal: Death cross with shrinking red bars
        sell_signal = death_cross & bar_shrink

        # Return buy and sell signals
        return buy_signal - sell_signal  # 1 for buy, -1 for sell, 0 for no action

    @staticmethod
    def ret_rsi_boll_sig(d):
        c = d.close
        high = d.high
        low = d.low

        # Parameters for Bollinger Bands and RSI
        rsi_period = 14
        boll_period = 20
        boll_std = 2

        # Calculate the RSI (Relative Strength Index)
        delta = c.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=rsi_period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=rsi_period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))

        # Calculate Bollinger Bands
        rolling_mean = c.rolling(window=boll_period).mean()
        rolling_std = c.rolling(window=boll_period).std()
        boll_upper = rolling_mean + (boll_std * rolling_std)
        boll_lower = rolling_mean - (boll_std * rolling_std)
        boll_middle = rolling_mean

        # Buy signal condition: RSI crosses above 50 and price is above the middle Bollinger Band
        buy_signal = ((rsi.shift(1) <= 50) & (rsi > 50) & (c > boll_middle))

        # Sell signal condition: Price breaks below the middle Bollinger Band and reaches the lower Bollinger Band
        sell_signal = ((c.shift(1) > boll_middle) & (
            c < boll_middle) & (c <= boll_lower))

        # Signal output: 1 for buy, -1 for sell, 0 for no action
        s = buy_signal.astype(int) - sell_signal.astype(int)

        return s

    @staticmethod
    def ret_ma50_cross_sig_price(d):
        c = d.close
        volume = d.volume
        long_T = 30
        short_T = 5

        # 计算移动平均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算EMV
        emv = (c.diff() / c.shift(1) * volume).rolling(window=short_T).sum()

        # 判断移动平均线是否大于50
        ma_condition = ma_long > 50

        # 判断EMV是否穿越0轴
        emv_cross_up = (emv > 0) & (emv.shift(1) <= 0)  # EMV由下往上穿越0轴
        emv_cross_down = (emv < 0) & (emv.shift(1) >= 0)  # EMV由上往下穿越0轴

        # 生成信号
        buy_signal = ma_condition & emv_cross_up  # 中期买进信号
        sell_signal = ma_condition & emv_cross_down  # 中期卖出信号

        # 将信号转换为数值
        signal = buy_signal.astype(int) - sell_signal.astype(int)

        return signal

    @staticmethod
    def ret_ma_bbi_rsi_sig_price(d):
        c = d.close      # 收盘价
        v = d.volume     # 成交量
        long_T = 30      # 长期均线窗口
        short_T = 5      # 短期均线窗口
        kdj_period = 14  # KDJ指标的计算周期
        bbi_short_T = 5  # BBI的短期均线周期
        bbi_long_T = 10  # BBI的长期均线周期

        # 计算短期和长期的移动平均
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算KDJ指标
        low_min = c.rolling(window=kdj_period).min()
        high_max = c.rolling(window=kdj_period).max()
        rsv = 100 * (c - low_min) / (high_max - low_min)  # RSV值
        k = rsv.ewm(com=2, adjust=False).mean()  # K值
        d = k.ewm(com=2, adjust=False).mean()  # D值
        j = 3 * k - 2 * d  # J值

        # KDJ金叉条件：K线突破D线且KDJ值小于25
        kdj_cross_up = (k > d) & (k.shift(1) <= d.shift(1))  # KDJ金叉
        kdj_below_25 = j < 25  # KDJ值小于25
        buy_signal_kdj = kdj_cross_up & kdj_below_25

        # KDJ死叉条件：D线突破K线且KDJ值大于75
        kdj_cross_down = (k < d) & (k.shift(1) >= d.shift(1))  # KDJ死叉
        kdj_above_75 = j > 75  # KDJ值大于75
        sell_signal_kdj = kdj_cross_down & kdj_above_75

        # 计算BBI（买卖平衡指数）
        ma_5 = c.rolling(window=5).mean()
        ma_10 = c.rolling(window=10).mean()
        ma_20 = c.rolling(window=20).mean()
        bbi = (ma_5 + ma_10 + ma_20) / 3  # BBI = (5日均线 + 10日均线 + 20日均线) / 3

        # BBI突破双底颈线：BBI突破前期低点
        bbi_double_bottom = bbi > bbi.shift(1)  # BBI突破前一日

        # 买入信号：MA在50之上且KDJ金叉且KDJ小于25，同时BBI突破双底颈线
        buy_signal_bbi = (ma_short > 50) & buy_signal_kdj & bbi_double_bottom

        # 卖出信号：KDJ死叉且KDJ大于75
        sell_signal_bbi = sell_signal_kdj

        # 合并信号：1表示买入，-1表示卖出，0表示没有信号
        signals = buy_signal_bbi.astype(int) - sell_signal_bbi.astype(int)

        return signals

    @staticmethod
    def ret_ma_short_long_cross_sig_price(d):
        c = d.close
        long_T1 = 30  # 长期平均线1
        long_T2 = 60  # 长期平均线2
        short_T = 5   # 短期平均线

        # 计算短期和长期移动平均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long1 = c.rolling(window=long_T1).mean()
        ma_long2 = c.rolling(window=long_T2).mean()

        # 确保长期移动平均线大于50
        ma_condition = (ma_long1 > 50) & (ma_long2 > 50)

        # 判断短期平均线是否上穿长期平均线
        crossover_signal1 = (ma_short > ma_long1) & (
            ma_short.shift(1) <= ma_long1.shift(1))
        crossover_signal2 = (ma_short > ma_long2) & (
            ma_short.shift(1) <= ma_long2.shift(1))

        # 结合条件，产生最终信号
        signal = ma_condition.astype(
            int) * (crossover_signal1 | crossover_signal2).astype(int)

        return signal

    @staticmethod
    def ret_mfi_sig_price(d):
        c = d.close
        high = d.high
        low = d.low
        volume = d.volume

        # MFI 计算
        typical_price = (high + low + c) / 3
        money_flow = typical_price * volume
        positive_flow = (typical_price > typical_price.shift(1)) * money_flow
        negative_flow = (typical_price < typical_price.shift(1)) * money_flow

        # 计算 MFI
        mfi = 100 * positive_flow.rolling(window=14).sum() / (
            positive_flow.rolling(window=14).sum() + negative_flow.rolling(window=14).sum())

        # 买入信号: MFI 上穿 80
        buy_signal = (mfi.shift(1) < 80) & (mfi >= 80)

        # 卖出信号: MFI 下穿 20
        sell_signal = (mfi.shift(1) > 20) & (mfi <= 20)

        # 输出信号：1 表示买入，-1 表示卖出，0 表示无操作
        signal = pd.Series(0, index=d.index)
        signal[buy_signal] = 1  # 买入信号
        signal[sell_signal] = -1  # 卖出信号

        return signal

    @staticmethod
    def ret_stc_sig_price(d):
        c = d.close
        long_T = 30
        short_T = 5

        # 计算短期和长期的均线
        ma_short = c.rolling(window=short_T).mean()
        ma_long = c.rolling(window=long_T).mean()

        # 计算短期和长期的最高价与最低价
        high_short = c.rolling(window=short_T).max()
        high_long = c.rolling(window=long_T).max()
        low_short = c.rolling(window=short_T).min()
        low_long = c.rolling(window=long_T).min()

        # 计算成交量的短期和长期和
        vol_short = c.rolling(window=short_T).sum()
        vol_long = c.rolling(window=long_T).sum()

        # 计算上穿下穿的条件
        condu = ((((ma_short > ma_long).astype(int).diff() == 1).astype(
            int) + (high_short >= high_long).astype(int)) == 2).astype(int)
        condd = ((((ma_short < ma_long).astype(int).diff() == 1).astype(
            int) + (low_short <= low_long).astype(int)) == 2).astype(int)

        # 计算成交量的相对值
        ft = vol_short / vol_long

        # 计算买入卖出信号的基础信号
        s = condu - condd

        # --- STC Indicator Calculation ---

        # 计算快速和慢速移动平均线
        fast_k = c.rolling(window=short_T).mean()  # 快速移动平均
        slow_d = c.rolling(window=long_T).mean()   # 慢速移动平均

        # 计算 STC 值
        stc = 100 * (fast_k - slow_d) / slow_d  # 简化的 STC 公式，实际可根据需要调整

        # 根据 STC 判断买入卖出信号
        buy_signal = (stc > 25).astype(int)  # 上穿 25
        sell_signal = (stc < 75).astype(int)  # 下穿 75

        # 最终的信号：买卖信号
        final_signal = buy_signal - sell_signal

        return final_signal

    @staticmethod
    def hide001(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_1 (i=0, j=1)"""
        return (df['close'].shift(0) / df['close'].shift(1)).fillna(1)

    @staticmethod
    def hide002(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_3 (i=0, j=3)"""
        return (df['close'].shift(0) / df['close'].shift(3)).fillna(1)

    @staticmethod
    def hide003(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_5 (i=0, j=5)"""
        return (df['close'].shift(0) / df['close'].shift(5)).fillna(1)

    @staticmethod
    def hide004(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_10 (i=0, j=10)"""
        return (df['close'].shift(0) / df['close'].shift(10)).fillna(1)

    @staticmethod
    def hide005(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_20 (i=0, j=20)"""
        return (df['close'].shift(0) / df['close'].shift(20)).fillna(1)

    @staticmethod
    def hide006(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_30 (i=0, j=30)"""
        return (df['close'].shift(0) / df['close'].shift(30)).fillna(1)

    @staticmethod
    def hide007(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_60 (i=0, j=60)"""
        return (df['close'].shift(0) / df['close'].shift(60)).fillna(1)

    # ------------------------- i=1 的因子 (跳过 j=1) -------------------------
    @staticmethod
    def hide008(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_3 (i=1, j=3)"""
        return (df['close'].shift(1) / df['close'].shift(3)).fillna(1)

    @staticmethod
    def hide009(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_5 (i=1, j=5)"""
        return (df['close'].shift(1) / df['close'].shift(5)).fillna(1)

    @staticmethod
    def hide010(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_10 (i=1, j=10)"""
        return (df['close'].shift(1) / df['close'].shift(10)).fillna(1)

    @staticmethod
    def hide011(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_20 (i=1, j=20)"""
        return (df['close'].shift(1) / df['close'].shift(20)).fillna(1)

    @staticmethod
    def hide012(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_30 (i=1, j=30)"""
        return (df['close'].shift(1) / df['close'].shift(30)).fillna(1)

    @staticmethod
    def hide013(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_60 (i=1, j=60)"""
        return (df['close'].shift(1) / df['close'].shift(60)).fillna(1)

    # ------------------------- i=3 的因子 (跳过 j=3) -------------------------
    @staticmethod
    def hide014(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_1 (i=3, j=1)"""
        return (df['close'].shift(3) / df['close'].shift(1)).fillna(1)

    @staticmethod
    def hide015(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_5 (i=3, j=5)"""
        return (df['close'].shift(3) / df['close'].shift(5)).fillna(1)

    @staticmethod
    def hide016(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_10 (i=3, j=10)"""
        return (df['close'].shift(3) / df['close'].shift(10)).fillna(1)

    @staticmethod
    def hide017(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_20 (i=3, j=20)"""
        return (df['close'].shift(3) / df['close'].shift(20)).fillna(1)

    @staticmethod
    def hide018(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_30 (i=3, j=30)"""
        return (df['close'].shift(3) / df['close'].shift(30)).fillna(1)

    @staticmethod
    def hide019(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_60 (i=3, j=60)"""
        return (df['close'].shift(3) / df['close'].shift(60)).fillna(1)

    # ------------------------- i=5 的因子 (跳过 j=5) -------------------------
    @staticmethod
    def hide020(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_1 (i=5, j=1)"""
        return (df['close'].shift(5) / df['close'].shift(1)).fillna(1)

    @staticmethod
    def hide021(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_3 (i=5, j=3)"""
        return (df['close'].shift(5) / df['close'].shift(3)).fillna(1)

    @staticmethod
    def hide022(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_10 (i=5, j=10)"""
        return (df['close'].shift(5) / df['close'].shift(10)).fillna(1)

    @staticmethod
    def hide023(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_20 (i=5, j=20)"""
        return (df['close'].shift(5) / df['close'].shift(20)).fillna(1)

    @staticmethod
    def hide024(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_30 (i=5, j=30)"""
        return (df['close'].shift(5) / df['close'].shift(30)).fillna(1)

    @staticmethod
    def hide025(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_60 (i=5, j=60)"""
        return (df['close'].shift(5) / df['close'].shift(60)).fillna(1)

    # ------------------------- i=15 的因子 -------------------------
    @staticmethod
    def hide026(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_1 (i=15, j=1)"""
        return (df['close'].shift(15) / df['close'].shift(1)).fillna(1)

    @staticmethod
    def hide027(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_3 (i=15, j=3)"""
        return (df['close'].shift(15) / df['close'].shift(3)).fillna(1)

    @staticmethod
    def hide028(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_5 (i=15, j=5)"""
        return (df['close'].shift(15) / df['close'].shift(5)).fillna(1)

    @staticmethod
    def hide029(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_10 (i=15, j=10)"""
        return (df['close'].shift(15) / df['close'].shift(10)).fillna(1)

    @staticmethod
    def hide030(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_20 (i=15, j=20)"""
        return (df['close'].shift(15) / df['close'].shift(20)).fillna(1)

    @staticmethod
    def hide031(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_30 (i=15, j=30)"""
        return (df['close'].shift(15) / df['close'].shift(30)).fillna(1)

    @staticmethod
    def hide032(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_60 (i=15, j=60)"""
        return (df['close'].shift(15) / df['close'].shift(60)).fillna(1)

    @staticmethod
    def c_hide_001(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_1 (i=0, j=1)"""
        return abs((df['close'].shift(0) / df['close'].shift(1)).fillna(1) - 1)

    @staticmethod
    def c_hide_002(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_3 (i=0, j=3)"""
        return abs((df['close'].shift(0) / df['close'].shift(3)).fillna(1) - 1)

    @staticmethod
    def c_hide_003(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_5 (i=0, j=5)"""
        return abs((df['close'].shift(0) / df['close'].shift(5)).fillna(1) - 1)

    @staticmethod
    def c_hide_004(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_10 (i=0, j=10)"""
        return abs((df['close'].shift(0) / df['close'].shift(10)).fillna(1) - 1)

    @staticmethod
    def c_hide_005(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_20 (i=0, j=20)"""
        return abs((df['close'].shift(0) / df['close'].shift(20)).fillna(1) - 1)

    @staticmethod
    def c_hide_006(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_30 (i=0, j=30)"""
        return abs((df['close'].shift(0) / df['close'].shift(30)).fillna(1) - 1)

    @staticmethod
    def c_hide_007(df: pd.DataFrame) -> pd.Series:
        """close_shift_0 / close_shift_60 (i=0, j=60)"""
        return abs((df['close'].shift(0) / df['close'].shift(60)).fillna(1) - 1)

    # ------------------------- i=1 的因子 (跳过 j=1) -------------------------
    @staticmethod
    def c_hide_008(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_3 (i=1, j=3)"""
        return abs((df['close'].shift(1) / df['close'].shift(3)).fillna(1) - 1)

    @staticmethod
    def c_hide_009(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_5 (i=1, j=5)"""
        return abs((df['close'].shift(1) / df['close'].shift(5)).fillna(1) - 1)

    @staticmethod
    def c_hide_010(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_10 (i=1, j=10)"""
        return abs((df['close'].shift(1) / df['close'].shift(10)).fillna(1) - 1)

    @staticmethod
    def c_hide_011(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_20 (i=1, j=20)"""
        return abs((df['close'].shift(1) / df['close'].shift(20)).fillna(1) - 1)

    @staticmethod
    def c_hide_012(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_30 (i=1, j=30)"""
        return abs((df['close'].shift(1) / df['close'].shift(30)).fillna(1) - 1)

    @staticmethod
    def c_hide_013(df: pd.DataFrame) -> pd.Series:
        """close_shift_1 / close_shift_60 (i=1, j=60)"""
        return abs((df['close'].shift(1) / df['close'].shift(60)).fillna(1) - 1)

    # ------------------------- i=3 的因子 (跳过 j=3) -------------------------
    @staticmethod
    def c_hide_014(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_1 (i=3, j=1)"""
        return abs((df['close'].shift(3) / df['close'].shift(1)).fillna(1) - 1)

    @staticmethod
    def c_hide_015(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_5 (i=3, j=5)"""
        return abs((df['close'].shift(3) / df['close'].shift(5)).fillna(1) - 1)

    @staticmethod
    def c_hide_016(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_10 (i=3, j=10)"""
        return abs((df['close'].shift(3) / df['close'].shift(10)).fillna(1) - 1)

    @staticmethod
    def c_hide_017(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_20 (i=3, j=20)"""
        return abs((df['close'].shift(3) / df['close'].shift(20)).fillna(1) - 1)

    @staticmethod
    def c_hide_018(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_30 (i=3, j=30)"""
        return abs((df['close'].shift(3) / df['close'].shift(30)).fillna(1) - 1)

    @staticmethod
    def c_hide_019(df: pd.DataFrame) -> pd.Series:
        """close_shift_3 / close_shift_60 (i=3, j=60)"""
        return abs((df['close'].shift(3) / df['close'].shift(60)).fillna(1) - 1)

    # ------------------------- i=5 的因子 (跳过 j=5) -------------------------
    @staticmethod
    def c_hide_020(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_1 (i=5, j=1)"""
        return abs((df['close'].shift(5) / df['close'].shift(1)).fillna(1) - 1)

    @staticmethod
    def c_hide_021(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_3 (i=5, j=3)"""
        return abs((df['close'].shift(5) / df['close'].shift(3)).fillna(1) - 1)

    @staticmethod
    def c_hide_022(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_10 (i=5, j=10)"""
        return abs((df['close'].shift(5) / df['close'].shift(10)).fillna(1) - 1)

    @staticmethod
    def c_hide_023(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_20 (i=5, j=20)"""
        return abs((df['close'].shift(5) / df['close'].shift(20)).fillna(1) - 1)

    @staticmethod
    def c_hide_024(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_30 (i=5, j=30)"""
        return abs((df['close'].shift(5) / df['close'].shift(30)).fillna(1) - 1)

    @staticmethod
    def c_hide_025(df: pd.DataFrame) -> pd.Series:
        """close_shift_5 / close_shift_60 (i=5, j=60)"""
        return abs((df['close'].shift(5) / df['close'].shift(60)).fillna(1) - 1)

    # ------------------------- i=15 的因子 -------------------------
    @staticmethod
    def c_hide_026(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_1 (i=15, j=1)"""
        return abs((df['close'].shift(15) / df['close'].shift(1)).fillna(1) - 1)

    @staticmethod
    def c_hide_027(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_3 (i=15, j=3)"""
        return abs((df['close'].shift(15) / df['close'].shift(3)).fillna(1) - 1)

    @staticmethod
    def c_hide_028(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_5 (i=15, j=5)"""
        return abs((df['close'].shift(15) / df['close'].shift(5)).fillna(1) - 1)

    @staticmethod
    def c_hide_029(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_10 (i=15, j=10)"""
        return abs((df['close'].shift(15) / df['close'].shift(10)).fillna(1) - 1)

    @staticmethod
    def c_hide_030(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_20 (i=15, j=20)"""
        return abs((df['close'].shift(15) / df['close'].shift(20)).fillna(1) - 1)

    @staticmethod
    def c_hide_031(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_30 (i=15, j=30)"""
        return abs((df['close'].shift(15) / df['close'].shift(30)).fillna(1) - 1)

    @staticmethod
    def c_hide_032(df: pd.DataFrame) -> pd.Series:
        """close_shift_15 / close_shift_60 (i=15, j=60)"""
        return abs((df['close'].shift(15) / df['close'].shift(60)).fillna(1) - 1)

    @staticmethod
    def open(d):
        return d['open']

    @staticmethod
    def high(d):
        return d['high']

    @staticmethod
    def low(d):
        return d['low']

    @staticmethod
    def close(d):
        return d['close']

    @staticmethod
    def volume(d):
        return d['volume']

    @staticmethod
    def turnover(d):
        return d['turnover']

    @staticmethod
    def close_time(d):
        return d['close_time']

    @staticmethod
    def trade_count(d):
        return d['trade_count']

    @staticmethod
    def taker_buy_volume(d):
        return d['taker_buy_volume']

    @staticmethod
    def taker_buy_turnover(d):
        return d['taker_buy_turnover']
