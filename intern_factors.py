import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from typing import Union

class InternFactors:

    # wj
    @staticmethod
    def c_wj001(df, ema_period=20, atr_period=10, multiplier=2):
        '''凯尔特纳通道：基于ATR的波动通道'''
        ema = df['close'].ewm(span=ema_period, adjust=False).mean()
        
        # 计算ATR
        high_low = df['high'] - df['low']
        high_close = abs(df['high'] - df['close'].shift(1))
        low_close = abs(df['low'] - df['close'].shift(1))
        true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = true_range.ewm(span=atr_period, adjust=False).mean()
        
        # 计算通道宽度
        channel_width = multiplier * atr
        
        factor = (df['close'] - (ema + channel_width)) / (2 * channel_width)
        
        return factor


    @staticmethod
    def c_wj002(df):
        """
        改进点：
        1. 结合成交量分布和价格位置双重确认
        2. 增加交易意图分析（主动买入/卖出）
        3. 使用动态时间窗口
        """
        # 动态窗口：根据波动率调整回溯期
        volatility = df['close'].pct_change().rolling(96).std()
        
        # 处理NaN和inf问题 - 填充缺失值，避免除以零
        volatility = volatility.fillna(volatility.mean())  # 填充NaN为均值
        volatility = volatility.replace(0, 1e-6)           # 避免除以零
        volatility = np.clip(volatility, 1e-6, None)        # 确保最小正值
        
        # 计算窗口大小并确保为整数
        window_size = (960 / (volatility * 100 + 1))
        window_size = np.nan_to_num(window_size, nan=480)   # 替换NaN为默认值
        window_size = np.clip(window_size, 480, 1440).astype(int)
        
        # 预计算关键指标
        avg_price = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        ret = (df['close'] - df['open']) / df['open']
        abs_ret = np.abs(ret)
        buy_power = df['taker_buy_volume'] / df['volume']
        
        # 核心指标：量价确认得分
        # 结合价格变动方向、主动买入比例和交易规模
        S = np.sign(ret) * abs_ret * buy_power * np.log1p(df['volume'])
        
        # 初始化因子值数组
        factor_values = np.full(len(df), np.nan)
        
        # 找到有效的起始索引（确保有足够数据）
        start_idx = max(np.max(window_size), 96) + 1
        
        for i in range(start_idx, len(df)):
            w_size = window_size[i]
            start_idx = i - w_size
            end_idx = i - 1
            
            # 提取窗口数据
            window_avg_price = avg_price.iloc[start_idx:end_idx].values
            window_volume = df['volume'].iloc[start_idx:end_idx].values
            window_S = S.iloc[start_idx:end_idx].values
            
            # 按S值降序排序（聪明钱交易在前）
            sorted_idx = np.argsort(-window_S)
            sorted_volume = window_volume[sorted_idx]
            sorted_avg_price = window_avg_price[sorted_idx]
            
            # 动态阈值（基于波动率）
            threshold_ratio = 0.3 - 0.15 * (volatility[i] / volatility.max())  # 波动大时降低阈值
            threshold = np.sum(window_volume) * threshold_ratio
            
            # 识别聪明钱交易
            cum_volume = np.cumsum(sorted_volume)
            smart_mask = cum_volume <= threshold
            
            if np.any(smart_mask):
                # 计算聪明钱VWAP（加权平均价）
                smart_vwap = np.sum(sorted_avg_price[smart_mask] * 
                                sorted_volume[smart_mask]) / np.sum(sorted_volume[smart_mask])
                
                # 计算整体VWAP
                all_vwap = np.sum(window_avg_price * window_volume) / np.sum(window_volume)
                
                # 添加市场状态调整
                if all_vwap > window_avg_price[-96:].mean():  # 近期上涨
                    factor_values[i] = smart_vwap / all_vwap
                else:  # 近期下跌
                    factor_values[i] = 2 - (smart_vwap / all_vwap)  # 反转因子方向
        
        return pd.Series(-factor_values, index=df.index)




    # lzj
    @staticmethod
    def c_lzj001(df):
        def ts_mad(series: pd.Series, window: int) -> pd.Series:
            """
            计算滚动中位数绝对偏差(MAD)
            
            Parameters:
            -----------
            series : pd.Series
                输入时间序列
            window : int
                滚动窗口大小
                
            Returns:
            --------
            pd.Series
                滚动MAD结果
            """
            def mad_func(x: np.ndarray) -> float:
                return np.median(np.abs(x - np.median(x)))
            return series.rolling(window=window, min_periods=1).apply(mad_func, raw=True)
        def decay_linear(series: pd.Series, window: int) -> pd.Series:
            """
            计算时间序列的线性衰减加权移动平均
            (最近的数据点权重最高)
            
            Parameters:
            -----------
            series : pd.Series
                输入时间序列
            window : int
                滚动窗口大小
                
            Returns:
            --------
            pd.Series
                线性衰减加权移动平均结果
                
            Raises:
            -------
            TypeError
                当输入不是pd.Series时
            ValueError
                当窗口大小<=0时
            """
            if not isinstance(series, pd.Series):
                raise TypeError("输入数据必须是pandas Series")
            if window <= 0:
                raise ValueError("窗口大小必须是正整数")

            def weighted_average(x: np.ndarray) -> float:
                weights = np.linspace(1, 0, num=len(x))  # 线性衰减权重
                return np.dot(x, weights) / weights.sum()
            
            return series.rolling(window=window, min_periods=1).apply(
                weighted_average, raw=True)
        ma=df['close'].rolling(15).mean()
        mad_result = ts_mad((df['close']-ma)/ma, window=60)
        return decay_linear(mad_result, window=20)