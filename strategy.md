## 1、计算当前时刻各交易所当前买盘/卖盘前10档总挂单量（cur_buy/cur_sell）

## 2、计算当前时刻各交易所中间价（(买一价+卖一价)/2），并求全市场平均值（cur_mid_price）

## 3、计算10秒前各交易所买盘/卖盘前10档总挂单量（past_buy/past_sell）
## 4、当前订单不平衡度：cur_order = cur_buy - cur_sell
## 5、历史订单不平衡度：past_order = past_buy - past_sell
## 6、当前时刻深度比率depth_ratio=cur_order/past_order

## 7、构建当前订单不平衡度布林带cur_order_boll：
### Ⅰ、上轨：cur_order的平均数值+2倍cur_order的标准差
### Ⅱ、中轨：cur_order的平均数值
### Ⅲ、下轨：cur_order的平均数值-2倍cur_order的标准差

## 8、构建历史订单不平衡度布林带past_order_boll：
### Ⅰ、上轨：past_order的平均数值+2倍past_order的标准差
### Ⅱ、中轨：past_order的平均数值
### Ⅲ、下轨：past_order的平均数值-2倍past_order的标准差

## 9、构建当前时刻深度比率布林带depth_ratio_boll：
### Ⅰ、上轨：depth_ratio的平均数值+1倍depth_ratio的标准差
### Ⅱ、中轨：depth_ratio的平均数值
### Ⅲ、下轨：depth_ratio的平均数值-1倍depth_ratio的标准差
## 10、做多信号：cur_order_boll上轨小于0，past_order_boll上轨小于0，depth_ratio_boll下轨小于0，以cur_mid_price下限价单
## 11、做空信号：cur_order_boll下轨小于0，past_order_boll下轨小于0，depth_ratio_boll下轨小于0，以cur_mid_price下限价单
## 12、平多信号：当前触发做空信号，且当前持有空单数量-未成交空单数量>=0.002时，平多0.002份（0.002是手动传入的数据）
## 13、平空信号：当前触发做多信号，且当前持有多单数量-未成交多单数量>=0.002时，平空0.002份（0.002是手动传入的数据）