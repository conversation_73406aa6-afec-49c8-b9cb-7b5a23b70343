import datetime
import time
import traceback
import numpy as np
import requests
import hmac
from hashlib import sha256
import pandas as pd
import configparser
import os
import csv
from btc_obtd import signal
from btc_obtd_ok import okx_strategy
from bitget_rest_trade import bitget_strategy

bn_spot_url = "https://api.binance.com"
bn_swap_url = "https://fapi.binance.com"
bn_swap_test_url = "https://testnet.binancefuture.com/"
bn_symbol = "BTCUSDT"
level = 100  # 要改杠杆在这里
bn_quantity = 0.002  # 要改下单量在这里， 1张合约=0.001BTC，最小下单量0.002BTC
config_file = 'config.ini'
last_datetime = None
okx_symbol = 'BTC-USDT-SWAP'

zhang_bn_api = "60c130919693fb4d0378e04dc1ced98ce14301dfdea952ac92c1b5aed2967fa2"
zhang_bn_secret = "4c5bb737a06614b3a116c3fa21febc401d2254de38307f2543f9e712b5030321"

xing_bn_api = "e69f8bb64cbc05110f3d40bb127336afe9388f0a425a884cfc6a11e470807fa8"
xing_bn_secret = "9856c9d8b78870427534021ccf5853b61b90499071e3c1094cb936989cce2f5e"

liu_bn_api = "YGezMeDyFYSL2EMP6ijwhyTnPmAvI3Ey2cfcmHSE8oHuVTWjHRubwZtXvReDZ05e"
liu_bn_secret = "y0zRiqMXZlX4z3BgyXQ7xuJWLrZtYHfg7Pcc1Ss70wOctKUaXRGFNsoRFVmvnJnV"

def ex_name(api, secret):
    if api == zhang_bn_api and secret == zhang_bn_secret:
        exchange_name = 'bn_zhang'
    elif api == xing_bn_api and secret == xing_bn_secret:
        exchange_name = 'bn_xing'
    elif api == liu_bn_api and secret == liu_bn_secret:
        exchange_name = 'bn_liu'
    else:
        exchange_name = '_'
    return exchange_name


# 读取配置文件
# config = configparser.ConfigParser()
# if os.path.exists(config_file):
#     config.read(config_file)
#     apiKey = config.get('credentials', 'apiKey', fallback="")
#     secretKey = config.get('credentials', 'secretKey', fallback="")
# else:
#     apiKey = ""
#     secretKey = ""

# apiKey = "ebe79bbc2545c4fb5bf8f92077644f13b9c82c8917a411e23656a896f955059c"
# secretKey = "6b091a9a9facae7ddc6326be054a79dbca33285a02da6e140465c3c303563338"


def get_timestamp():
    response = requests.get("https://data-api.binance.vision/api/v3/time")
    if response.status_code == 200:
        js = response.json()
        timestamp = js['serverTime']
    else:
        timestamp = int(time.time() * 1000)
    return timestamp


def get_tick(symbol):
    kline_url = bn_spot_url + "/api/v1/depth"
    limit = 10
    headers = {'Accept': 'application/json', 'Content-Type': 'application/json'}
    queryString = f"symbol={symbol}&limit={limit}"
    url = kline_url + "?" + queryString
    response = requests.get(url, headers=headers).json()
    bid_1 = float(response['bids'][0][0])
    ask_1 = float(response['asks'][0][0])
    return round(bid_1, 1), round(ask_1, 1)


# def django_signal():
#     global last_datetime
#     trade_signal = 0
#     babs = requests.get('http://101.36.110.16:8000/hfbtc/getMarketData/?limit=1')
#     if babs.status_code == 200:
#         data = babs.json()['data'][0]
#         dt = datetime.datetime.strptime(data['datetime'], '%Y-%m-%dT%H:%M:%SZ')
#         if last_datetime is None or last_datetime < dt:
#             trade_signal = data['trade_signal']
#             last_datetime = dt
#     return trade_signal


def getSign(data, secret):
    if not secret:
        print("SecretKey 为空")
        return ""
    secretKeyByte = secret.encode("utf-8")
    dataByte = data.encode("utf-8")
    hmacS = hmac.new(secretKeyByte, dataByte, sha256)
    signature = hmacS.hexdigest()
    return signature


# 设置杠杆
def setLevel(base_url, symbol, leve, api, secret):
    url = base_url + "/fapi/v1/leverage"
    timeStamp = get_timestamp()
    queryString = f"symbol={symbol}&leverage={leve}&recvWindow=5000&timestamp={timeStamp}"
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.post(url + "?" + queryString, headers=headers)
    js = response.json()
    print(js)
    if "code" in js:
        return False, js["msg"]
    return True, ""


# 更改持仓模式
def change_position_mode(base_url, api, secret):
    url = base_url + "/fapi/v1/positionSide/dual"
    timeStamp = get_timestamp()
    queryString = "dualSidePosition=false&recvWindow=5000&timestamp=" + str(timeStamp)
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.post(url + "?" + queryString, headers=headers)
    js = response.json()
    print(js)
    if "code" in js:
        if js["code"] == -4068:
            print("当前有持仓，无法更改持仓模式")
            return
        elif js["code"] == -4059:
            print("无需更改持仓模式")
            return
    print("单向持仓模式设置成功")
    return


# 交易
def trade(base_url, api, secret, symbol, side, position_side, order_type, amount, price=None,
          stop_price=None, active_price=None, callback_rate=None,
          stop_loss_limit=None, take_profit_limit=None):
    url = base_url + "/fapi/v1/order"  # 实盘api：base_swap_url + "/fapi/v1/order"
    timeStamp = get_timestamp()
    params = {'symbol': symbol,
              'side': side,
              'positionSide': position_side,
              'type': order_type,
              'quantity': amount,
              'recvWindow': 5000,
              'timestamp': timeStamp,
              'timeInForce': 'GTC'
              }
    if price is not None:
        params['price'] = price
    if stop_price is not None:
        params["stopPrice"] = stop_price
    if active_price is not None:
        params["activationPrice"] = active_price
    if callback_rate is not None:
        params["callbackRate"] = callback_rate
    # print(f"下单参数: {params}")
    queryString = "&".join([f"{key}={params[key]}" for key in params])
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.post(url + "?" + queryString, headers=headers)
    return response.json()


def open_long(base_url, api, secret, symbol, order_type, amount, price=None,
              stop_price=None, active_price=None, callback_rate=None,
              stop_loss_limit=None, take_profit_limit=None):
    ol = trade(base_url=base_url,
               api=api,
               secret=secret,
               symbol=symbol,
               side='BUY',
               position_side='LONG',
               order_type=order_type,
               amount=amount,
               price=price,
               stop_price=stop_price,
               active_price=active_price,
               callback_rate=callback_rate,
               stop_loss_limit=stop_loss_limit,
               take_profit_limit=take_profit_limit
               )
    return ol


def close_long(base_url, api, secret, symbol, order_type, amount, price=None,
               stop_price=None, active_price=None, callback_rate=None,
               stop_loss_limit=None, take_profit_limit=None):
    cl = trade(base_url=base_url,
               api=api,
               secret=secret,
               symbol=symbol,
               side='SELL',
               position_side='LONG',
               order_type=order_type,
               amount=amount,
               price=price,
               stop_price=stop_price,
               active_price=active_price,
               callback_rate=callback_rate,
               stop_loss_limit=stop_loss_limit,
               take_profit_limit=take_profit_limit)
    return cl


def open_short(base_url, api, secret, symbol, order_type, amount, price=None,
               stop_price=None, active_price=None, callback_rate=None,
               stop_loss_limit=None, take_profit_limit=None):
    ost = trade(base_url=base_url,
                api=api,
                secret=secret,
                symbol=symbol,
                side='SELL',
                position_side='SHORT',
                order_type=order_type,
                amount=amount,
                price=price,
                stop_price=stop_price,
                active_price=active_price,
                callback_rate=callback_rate,
                stop_loss_limit=stop_loss_limit,
                take_profit_limit=take_profit_limit
                )
    return ost


def close_short(base_url, api, secret, symbol, order_type, amount, price=None,
                stop_price=None, active_price=None, callback_rate=None,
                stop_loss_limit=None, take_profit_limit=None):
    cs = trade(base_url=base_url,
               api=api,
               secret=secret,
               symbol=symbol,
               side='BUY',
               position_side='SHORT',
               order_type=order_type,
               amount=amount,
               price=price,
               stop_price=stop_price,
               active_price=active_price,
               callback_rate=callback_rate,
               stop_loss_limit=stop_loss_limit,
               take_profit_limit=take_profit_limit)
    return cs


def get_balance(base_url, api, secret, cash, symbol):
    url = base_url + "/fapi/v3/account"
    timeStamp = get_timestamp()
    queryString = f"timestamp={timeStamp}&recvWindow=5000"
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.get(url + "?" + queryString, headers=headers).json()
    assets = response.get('assets', [])
    cash_balance = next((float(item['availableBalance']) for item in assets if item.get('asset') == cash), 0)
    short_position = next(
        (float(pos['positionAmt']) for pos in response.get('positions', []) if
         pos['symbol'] == symbol and pos['positionSide'] == 'SHORT'), 0)
    long_position = next(
        (float(pos['positionAmt']) for pos in response.get('positions', []) if
         pos['symbol'] == symbol and pos['positionSide'] == 'LONG'), 0)
    return cash_balance, long_position, short_position


def get_position(base_url, symbol, api, secret):
    long_posi, short_posi, long_entryprice, short_entryprice = 0, 0, 0, 0
    url = base_url + "/fapi/v2/positionRisk"
    timeStamp = get_timestamp()
    queryString = f"symbol={symbol}&timestamp={timeStamp}&recvWindow=5000"
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.get(url + "?" + queryString, headers=headers, timeout=5)
    if response.status_code == 200:
        data = response.json()
        long_position = next((p for p in data
                              if p.get('positionSide') == 'LONG' and float(p.get('positionAmt', 0)) != 0),
                             None)
        short_position = next((p for p in data
                               if p.get('positionSide') == 'SHORT' and float(p.get('positionAmt', 0)) != 0),
                              None)
        long_posi = float(long_position['positionAmt']) if long_position is not None else 0
        short_posi = abs(float(short_position['positionAmt'])) if short_position is not None else 0
        long_entryprice = float(long_position['entryPrice']) if long_position is not None else 0
        short_entryprice = float(short_position['entryPrice']) if short_position is not None else 0
    return long_posi, short_posi, long_entryprice, short_entryprice


def get_open_orders(base_url, symbol, api, secret):
    open_long_amount, open_short_amount, close_long_amount, close_short_amount = 0, 0, 0, 0
    close_long_price_list, close_short_price_list = [], []
    url = base_url + "/fapi/v1/openOrders"
    timeStamp = get_timestamp()
    queryString = f"symbol={symbol}&timestamp={timeStamp}&recvWindow=5000"
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.get(url + "?" + queryString, headers=headers, timeout=5)
    if response.status_code == 200:
        orders = response.json()
        if len(orders) > 0:
            for order in orders:
                if order['side'] == 'BUY' and order['positionSide'] == 'LONG':
                    open_long_amount += float(order['origQty'])
                if order['side'] == 'SELL' and order['positionSide'] == 'SHORT':
                    open_short_amount += float(order['origQty'])
                if order['side'] == 'SELL' and order['positionSide'] == 'LONG':
                    close_long_amount += float(order['origQty'])
                    close_long_price_list.append(float(order['price']))
                if order['side'] == 'BUY' and order['positionSide'] == 'SHORT':
                    close_short_amount += float(order['origQty'])
                    close_short_price_list.append(float(order['price']))
    return open_long_amount, open_short_amount, close_long_amount, close_short_amount, close_long_price_list, close_short_price_list


def fetch_open_order(base_url, symbol, order_id, api, secret):
    order = None
    url = base_url + "/fapi/v1/openOrder"
    timeStamp = get_timestamp()
    queryString = f"symbol={symbol}&orderId={order_id}&timestamp={timeStamp}&recvWindow=5000"
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.get(url + "?" + queryString, headers=headers, timeout=5)
    if response.status_code == 200:
        order = response.json()
    return order


def cancel_all_orders(base_url, symbol, api, secret):
    url = base_url + "/fapi/v1/allOpenOrders"
    timeStamp = get_timestamp()
    queryString = f"symbol={symbol}&timestamp={timeStamp}&recvWindow=5000"
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.delete(url + "?" + queryString, headers=headers, timeout=5).json()
    return response


def cancel_order(base_url, symbol, order_id, api, secret):
    url = base_url + "/fapi/v1/order"
    timeStamp = get_timestamp()
    queryString = f"symbol={symbol}&orderId={order_id}&timestamp={timeStamp}&recvWindow=5000"
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.delete(url + "?" + queryString, headers=headers, timeout=5).json()
    return response


def bn_strategy(base_url, api, secret, symbol, amount, long_sgnl, short_sgnl):
    cash_balance, _, _ = get_balance(base_url=base_url, cash='USDT', symbol=symbol, api=api, secret=secret)
    long_posi, short_posi, long_entryprice, short_entryprice = get_position(base_url, symbol, api, secret)
    (open_long_amount, open_short_amount,
     close_long_amount, close_short_amount,
     close_long_price_list, close_short_price_list) = get_open_orders(base_url, symbol, api, secret)
    bid1, ask1 = get_tick(symbol=symbol)

    if long_sgnl:
        ol = open_long(base_url=base_url, api=api, secret=secret, symbol=symbol, order_type='LIMIT', amount=amount, price=ask1)
        print(ex_name(api, secret))
        print(ol)
        if short_posi - close_short_amount >= amount:
            cst = close_short(base_url=base_url, api=api, secret=secret, symbol=symbol, order_type='LIMIT', amount=amount, price=ask1)
            print(cst)

    if short_sgnl:
        ost = open_short(base_url=base_url, api=api, secret=secret, symbol=symbol, order_type='LIMIT', amount=amount, price=bid1)
        print(ex_name(api, secret))
        print(ost)
        if long_posi - close_long_amount >= amount:
            clg = close_long(base_url=base_url, api=api, secret=secret, symbol=symbol, order_type='LIMIT', amount=amount, price=bid1)
            print(clg)


if __name__ == "__main__":
    while True:
        try:
            long_signal, short_signal = signal()
            okx_strategy(symbol=okx_symbol, long_signal=long_signal, short_signal=short_signal)
            
            bn_strategy(base_url=bn_swap_test_url, api=zhang_bn_api, secret=zhang_bn_secret, symbol=bn_symbol,
                        amount=0.002, long_sgnl=long_signal, short_sgnl=short_signal)
            bn_strategy(base_url=bn_swap_test_url, api=xing_bn_api, secret=xing_bn_secret, symbol=bn_symbol,
                        amount=0.002, long_sgnl=long_signal, short_sgnl=short_signal)
            bn_strategy(base_url=bn_swap_url, api=liu_bn_api, secret=liu_bn_secret, symbol=bn_symbol,
                        amount=0.002, long_sgnl=long_signal, short_sgnl=short_signal)
                        
            bitget_strategy(symbol="BTCUSDT", long_sgnl=long_signal, short_sgnl=short_signal)
            
        except Exception as err:
            print(err)
            traceback.print_exc()





