
import json
import time
import numpy as np
import pandas as pd
import requests
import hmac
from hashlib import sha256


xing_bn_api = "e69f8bb64cbc05110f3d40bb127336afe9388f0a425a884cfc6a11e470807fa8"
xing_bn_secret = "9856c9d8b78870427534021ccf5853b61b90499071e3c1094cb936989cce2f5e"

liu_bn_api = "YGezMeDyFYSL2EMP6ijwhyTnPmAvI3Ey2cfcmHSE8oHuVTWjHRubwZtXvReDZ05e"
liu_bn_secret = "y0zRiqMXZlX4z3BgyXQ7xuJWLrZtYHfg7Pcc1Ss70wOctKUaXRGFNsoRFVmvnJnV"

base_test_url = "https://testnet.binancefuture.com/"
base_swap_url = "https://fapi.binance.com"


def get_timestamp():
    response = requests.get("https://data-api.binance.vision/api/v3/time")
    if response.status_code == 200:
        js = response.json()
        timestamp = js['serverTime']
    else:
        timestamp = int(time.time() * 1000)
    return timestamp


def getSign(data, secret):
    if not secret:
        print("SecretKey 为空")
        return ""
    secretKeyByte = secret.encode("utf-8")
    dataByte = data.encode("utf-8")
    hmacS = hmac.new(secretKeyByte, dataByte, sha256)
    signature = hmacS.hexdigest()
    return signature

# 更改持仓模式
def change_position_mode(base_url, api, secret):
    url = base_url + "/fapi/v1/positionSide/dual"
    timeStamp = get_timestamp()
    queryString = "dualSidePosition=True&recvWindow=5000&timestamp=" + str(timeStamp)
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.post(url + "?" + queryString, headers=headers)
    js = response.json()
    print(js)


def setLevel(base_url, symbol, leve, api, secret):
    url = base_url + "/fapi/v1/leverage"
    timeStamp = get_timestamp()
    queryString = f"symbol={symbol}&leverage={leve}&recvWindow=5000&timestamp={timeStamp}"
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.post(url + "?" + queryString, headers=headers)
    js = response.json()
    print(js)


def get_balance(base_url, api, secret, symbol):
    url = base_url + "/fapi/v3/account"
    timeStamp = get_timestamp()
    queryString = f"timestamp={timeStamp}&recvWindow=5000"
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.get(url + "?" + queryString, headers=headers).json()
    assets = response.get('assets', [])
    return response
    
    
def get_position(base_url, symbol, api, secret):
    long_posi, short_posi, long_entryprice, short_entryprice = 0, 0, 0, 0
    url = base_url + "/fapi/v2/positionRisk"
    timeStamp = get_timestamp()
    queryString = f"symbol={symbol}&timestamp={timeStamp}&recvWindow=5000"
    sign = getSign(queryString, secret)
    queryString += "&signature=" + sign
    headers = {"X-MBX-APIKEY": api}
    response = requests.get(url + "?" + queryString, headers=headers, timeout=5)
    return response.json()
    
#chg_posi_mode = change_position_mode(base_swap_url, api=liu_bn_api, secret=liu_bn_secret)
#print(chg_posi_mode)
#setleve = setLevel(base_swap_url, symbol="BTCUSDT", leve=100, api=liu_bn_api, secret=liu_bn_secret)
#print(setleve)
positions = get_position(base_url=base_swap_url, symbol="BTCUSDT", api=liu_bn_api, secret=liu_bn_secret)
print(positions)
data = get_balance(base_url=base_swap_url, symbol="BTCUSDT", api=liu_bn_api, secret=liu_bn_secret)
usdt_info = next((asset for asset in data['assets'] if asset['asset'] == 'USDT'), None)
print(usdt_info)



