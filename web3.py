import websocket
import json
import os
import logging
import pickle
import time
import requests
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from online_data_service import Dataservice
from strategy_v1 import MaCrossATRStrategy

# ============================================================================
# 配置区域 - 在这里修改您的设置
# ============================================================================

# 启动模式配置
STARTUP_MODE = "true"  # 可选: "true"(加载历史状态), "false"(从零开始), "manual"(手动设置持仓)

# 手动持仓配置 - 只在 STARTUP_MODE = "manual" 时生效
MANUAL_POSITIONS = [
    {
        "direction": "long",      # "long" 或 "short"
        "price": 3500.0,         # 开仓价格
        "volume": 1.0,           # 持仓数量
        "datetime": "2024-01-15 10:00:00"  # 开仓时间(可选)
    },
    # 可以添加更多持仓
    # {
    #     "direction": "short",
    #     "price": 3600.0,
    #     "volume": 0.5
    # }
]

# 策略参数配置
STRATEGY_CONFIG = {
    "short_window": 10,           # 短期均线周期
    "long_window": 100,          # 长期均线周期
    "atr_window": 14,            # ATR计算窗口
    "atr_factor": 2.0,           # ATR系数
    "stop_loss_pct": 2.0,        # 止损百分比
    "profit_take_ratio": 0.8     # 回撤止盈比例
}

# 数据服务配置
DATA_CONFIG = {
    "symbol": "ETHUSDT",         # 交易对
    "interval": "15m"            # K线周期
}

# WebSocket数据配置
WS_DATA_COFIG = {
    "symbol": "ethusdt",         # 交易对(需小写)
    "interval": "15m"            # K线周期
}

# 信号推送配置
SIGNAL_CONFIG = {
    "title": "c_ETH@15min_test_v2"         # 信号标题
}

# WebSocket重连配置
WEBSOCKET_CONFIG = {
    "enable_reconnect": True,           # 是否启用自动重连
    "max_reconnect_attempts": 5,       # 最大重连次数 (0表示无限重连)
    "initial_reconnect_delay": 1,       # 初始重连延迟(秒)
    "max_reconnect_delay": 60           # 最大重连延迟(秒)
}

# 重连功能说明:
# 1. 当WebSocket连接断开时，系统会自动尝试重连
# 2. 重连采用指数退避策略，延迟时间逐渐增加
# 3. 支持最大重连次数限制，防止无限重连
# 4. websocket-client库会自动处理ping/pong心跳机制
# 5. 重连成功后会自动重新订阅数据流

# ============================================================================
# 以下代码无需修改
# ============================================================================


def send_signal(signal, title):
    url = 'https://cta.skyfffire.com/api/cta/signal/send/group'
    timestamp = int(time.time())*1000
    params = {
        "timestamp": timestamp,
        "title": title,
        "signal": signal,
    }
    try:
        response = requests.post(url, json=params, timeout=10)
        response.raise_for_status()
        logger.info(f"✅ 信号发送成功: {signal}")
    except Exception as e:
        logger.error(f"❌ 信号发送失败: {e}")


# 设置日志
def setup_logging():
    """设置日志配置"""
    # 获取当前脚本所在目录，确保logs文件夹在traditional_cta目录下
    script_dir = Path(__file__).parent.absolute()
    logs_dir = script_dir / "logs"
    logs_dir.mkdir(exist_ok=True)

    # 生成日志文件名（只精确到日期）
    current_date = datetime.now().strftime("%Y%m%d")
    log_filename = logs_dir / \
        f"trading_system_{DATA_CONFIG['symbol']}_{current_date}.log"

    # 打印日志文件路径以便确认
    print(f"📁 日志文件路径: {log_filename}")

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_filename, mode='w',
                                encoding='utf-8'),  # 追加模式
            logging.StreamHandler()  # 同时输出到控制台
        ],
        force=True  # 强制重新配置
    )

    return logging.getLogger(__name__)


# 初始化日志
logger = setup_logging()

# 全局变量
data_service = None
strategy = None
title = None  # 将在main函数中初始化
ws_instance = None  # WebSocket实例
reconnect_count = 0  # 重连次数
is_reconnecting = False  # 是否正在重连

# 状态文件路径
STATUS_DIR = Path("status")
POSITION_STATE_FILE = STATUS_DIR / \
    f"position_state_{DATA_CONFIG['symbol']}_v2.pkl"


def get_position_status_info():
    """获取详细的持仓状态信息"""
    if not strategy or not strategy.positions:
        return "无持仓"

    position_count = len(strategy.positions)

    # 统计多头和空头数量
    long_count = sum(
        1 for pos in strategy.positions if pos.direction == "long")
    short_count = sum(
        1 for pos in strategy.positions if pos.direction == "short")

    # 生成状态描述
    status_parts = []
    if long_count > 0:
        status_parts.append(f"{long_count}个多头")
    if short_count > 0:
        status_parts.append(f"{short_count}个空头")

    if status_parts:
        return f"{position_count}个持仓({', '.join(status_parts)})"
    else:
        return f"{position_count}个持仓"


def save_position_state():
    """保存持仓状态到文件"""
    if strategy:
        # 创建status文件夹
        STATUS_DIR.mkdir(exist_ok=True)

        state = {
            'positions': strategy.positions,
            'signal_history': strategy.signal_history,
            'last_short_ma': strategy.last_short_ma,
            'last_long_ma': strategy.last_long_ma,
            'last_cross_price': strategy.last_cross_price,  # 添加交叉价格
            'last_cross_type': strategy.last_cross_type,    # 添加交叉类型
            'timestamp': datetime.now()
        }
        try:
            with open(POSITION_STATE_FILE, 'wb') as f:
                pickle.dump(state, f)

            # 生成详细的持仓状态信息
            position_info = get_position_status_info()
            logger.info(f"💾 持仓状态已保存: {position_info}")
        except Exception as e:
            logger.error(f"❌ 保存持仓状态失败: {e}")


def load_position_state():
    """从文件加载持仓状态"""
    if POSITION_STATE_FILE.exists():
        try:
            with open(POSITION_STATE_FILE, 'rb') as f:
                state = pickle.load(f)

            # 生成详细的持仓状态信息
            positions = state['positions']
            if positions:
                position_count = len(positions)
                long_count = sum(
                    1 for pos in positions if pos.direction == "long")
                short_count = sum(
                    1 for pos in positions if pos.direction == "short")

                status_parts = []
                if long_count > 0:
                    status_parts.append(f"{long_count}个多头")
                if short_count > 0:
                    status_parts.append(f"{short_count}个空头")

                if status_parts:
                    position_info = f"{position_count}个持仓({', '.join(status_parts)})"
                else:
                    position_info = f"{position_count}个持仓"
            else:
                position_info = "无持仓"

            logger.info(f"📂 加载持仓状态: {position_info}")
            logger.info(f"   状态时间: {state['timestamp']}")
            return state
        except Exception as e:
            logger.error(f"❌ 加载持仓状态失败: {e}")
    return None


def rebuild_strategy_state_from_dataservice():
    """使用DataService的历史数据重建策略状态"""
    global strategy, data_service

    if not data_service:
        logger.warning("⚠️ 数据服务未初始化，无法重建策略状态")
        return False

    # 获取DataService中的历史K线数据
    historical_df = data_service.get_kline()

    if historical_df is None or len(historical_df) < strategy.long_window:
        logger.warning(
            f"⚠️ 历史数据不足，需要至少{strategy.long_window}根K线，当前只有{len(historical_df) if historical_df is not None else 0}根")
        return False

    logger.info("🔄 开始使用DataService数据重建策略状态...")
    logger.info(f"📊 分析{len(historical_df)}根历史K线数据...")

    # 转换为策略需要的格式
    import pandas as pd
    strategy_df = pd.DataFrame({
        'open': historical_df['open'],
        'high': historical_df['high'],
        'low': historical_df['low'],
        'close': historical_df['close'],
        'volume': historical_df['volume']
    })

    # 设置时间索引
    strategy_df.index = pd.to_datetime(historical_df['open_time'], unit='ms')

    # 逐根K线重建状态，但不保存每个信号到历史
    temp_signal_history = []

    for i in range(strategy.long_window, len(strategy_df)):
        current_data = strategy_df.iloc[:i+1]

        try:
            # 计算信号但不触发回调
            signal = strategy.calculate_signal_realtime(current_data)

            if signal and signal['signal'] != 0:
                temp_signal_history.append(signal)
                # 移除详细的重建信号日志，只保留最终结果

        except Exception as e:
            logger.error(f"❌ 重建第{i+1}根K线状态失败: {e}")

    # 只保留最近的几个重要信号
    strategy.signal_history = temp_signal_history[-10:] if temp_signal_history else [
    ]

    # 重建持仓的extreme_price
    if strategy.positions:
        logger.info("🔄 重建持仓的extreme_price...")
        for position in strategy.positions:
            # 找到开仓时间之后的所有K线数据
            position_start_time = position.datetime

            # 获取开仓后的历史数据
            mask = strategy_df.index >= position_start_time
            post_position_data = strategy_df[mask]

            if len(post_position_data) > 0:
                if position.direction == "long":
                    # 多头持仓：找到开仓后的最高价
                    max_high = post_position_data['high'].max()
                    if max_high > position.extreme_price:
                        old_extreme = position.extreme_price
                        position.extreme_price = max_high
                        # 更新止盈价格
                        position.take_profit_price = position.price * \
                            (1 + strategy.stop_loss_pct / 100)
                        logger.info(
                            f"📈 多头extreme_price更新: {old_extreme:.4f} → {position.extreme_price:.4f}")
                else:  # short
                    # 空头持仓：找到开仓后的最低价
                    min_low = post_position_data['low'].min()
                    if min_low < position.extreme_price:
                        old_extreme = position.extreme_price
                        position.extreme_price = min_low
                        # 更新止盈价格
                        position.take_profit_price = position.price * \
                            (1 - strategy.stop_loss_pct / 100)
                        logger.info(
                            f"📉 空头extreme_price更新: {old_extreme:.4f} → {position.extreme_price:.4f}")

    # 显示重建结果
    position_info = strategy.get_current_position_info()
    logger.info(f"✅ 策略状态重建完成")
    # logger.info(f"   最新价格: {historical_df['close'].iloc[-1]:.4f}")

    # 显示交叉状态信息
    if strategy.last_cross_price > 0:
        logger.info(
            f"✅ 重建交叉状态: {strategy.last_cross_type}叉 @ {strategy.last_cross_price:.4f}")
    else:
        logger.info("✅ 重建过程中未发现历史交叉记录")

    # 显示最后一个信号的信息
    if temp_signal_history:
        last_signal = temp_signal_history[-1]
        logger.info(
            f"   最后信号: {last_signal['datetime']} - {last_signal['signal_type']} @ {last_signal['price']:.4f}")
        logger.info(
            f"   当时均线: MA{strategy.short_window}={last_signal['short_ma']:.4f} | MA{strategy.long_window}={last_signal['long_ma']:.4f}")

    # 如果有持仓，显示持仓详情
    if position_info['positions_count'] > 0:
        logger.info(
            f"   持仓详情: {position_info['direction']} @ {position_info['avg_price']:.4f} (数量: {position_info['total_volume']})")

        # 显示每个持仓的详细信息，包括extreme_price
        for i, pos in enumerate(strategy.positions):
            direction_emoji = "📈" if pos.direction == "long" else "📉"
            logger.info(
                f"   持仓{i+1}: {direction_emoji} {pos.direction} @ {pos.price:.4f}")
            logger.info(
                f"           止损价: {pos.stop_loss_price:.4f} | 止盈价: {pos.take_profit_price:.4f}")
            logger.info(
                f"           极值价: {pos.extreme_price:.4f} ({'最高价' if pos.direction == 'long' else '最低价'})")

    return True


def create_manual_positions(position_data):
    """
    根据传入的数据创建持仓对象列表

    参数:
    position_data: list - 持仓数据列表，每个元素包含持仓信息
                   格式: [
                       {
                           'direction': 'long',  # 'long' 或 'short'
                           'price': 3500.0,      # 开仓价格
                           'volume': 1.0,        # 持仓数量
                           'datetime': '2024-01-01 10:00:00'  # 开仓时间(可选)
                       },
                       ...
                   ]

    返回:
    list - Position对象列表
    """
    from strategy_v0 import Position

    if not position_data:
        return []

    positions = []
    stop_loss_pct = 2.0  # 默认止损百分比

    for i, pos_data in enumerate(position_data):
        try:
            # 验证必需字段
            if 'direction' not in pos_data or 'price' not in pos_data:
                logger.error(f"❌ 持仓数据{i+1}缺少必需字段 (direction, price)")
                continue

            direction = pos_data['direction'].lower()
            if direction not in ['long', 'short']:
                logger.error(f"❌ 持仓数据{i+1}方向无效: {direction}")
                continue

            price = float(pos_data['price'])
            volume = float(pos_data.get('volume', 1.0))

            # 处理开仓时间
            if 'datetime' in pos_data and pos_data['datetime']:
                try:
                    if isinstance(pos_data['datetime'], str):
                        entry_time = datetime.strptime(
                            pos_data['datetime'], "%Y-%m-%d %H:%M:%S")
                    else:
                        entry_time = pos_data['datetime']
                except ValueError:
                    logger.warning(f"⚠️ 持仓数据{i+1}时间格式错误，使用当前时间")
                    entry_time = datetime.now()
            else:
                entry_time = datetime.now()

            # 创建持仓对象
            position = Position(
                direction=direction,
                price=price,
                volume=volume,
                datetime=entry_time
            )

            # 设置止损止盈价格
            if direction == "long":
                position.stop_loss_price = price * (1 - stop_loss_pct / 100)
                position.take_profit_price = price * (1 + stop_loss_pct / 100)
                position.extreme_price = price  # 初始化最高价
            else:  # short
                position.stop_loss_price = price * (1 + stop_loss_pct / 100)
                position.take_profit_price = price * (1 - stop_loss_pct / 100)
                position.extreme_price = price  # 初始化最低价

            positions.append(position)

            direction_emoji = "📈" if direction == "long" else "📉"
            logger.info(
                f"✅ 创建持仓{i+1}: {direction_emoji} {direction} @ {price:.4f} (数量: {volume})")

        except Exception as e:
            logger.error(f"❌ 创建持仓数据{i+1}失败: {e}")
            continue

    return positions


def init_system(load_previous_state=True, manual_positions=None, manual_position_data=None):
    """
    初始化数据服务和策略

    参数:
    load_previous_state: bool - 是否加载之前的持仓状态，默认True
    manual_positions: list - 手动输入的持仓列表（交互式输入），默认None
    manual_position_data: list - 手动传入的持仓数据（参数传入），默认None
    """
    global data_service, strategy

    logger.info("🚀 启动增强版MA交叉策略交易系统...")
    logger.info("="*60)

    # 1. 根据选择决定是否加载之前的持仓状态
    saved_state = None
    if manual_position_data is not None:
        logger.info("📊 选择手动传入持仓模式")
        # 将传入的数据转换为Position对象
        manual_positions = create_manual_positions(manual_position_data)
    elif manual_positions is not None:
        logger.info("📝 选择手动输入持仓模式")
    elif load_previous_state:
        saved_state = load_position_state()
        if saved_state:
            logger.info("📂 选择加载历史状态模式")
        else:
            logger.info("📂 未找到历史状态文件，将从零开始")
    else:
        logger.info("🆕 选择从零开始模式，忽略历史状态")

    # 2. 初始化数据服务
    data_service = Dataservice(
        symbol=DATA_CONFIG["symbol"], interval=DATA_CONFIG["interval"])
    logger.info(f"✅ 数据服务初始化完成，获取了{len(data_service.get_kline())}根历史K线")

    # 3. 初始化策略
    strategy = MaCrossATRStrategy(
        data_service=data_service,
        short_window=STRATEGY_CONFIG["short_window"],
        long_window=STRATEGY_CONFIG["long_window"],
        atr_window=STRATEGY_CONFIG["atr_window"],
        atr_factor=STRATEGY_CONFIG["atr_factor"],
        stop_loss_pct=STRATEGY_CONFIG["stop_loss_pct"],
        profit_take_ratio=STRATEGY_CONFIG["profit_take_ratio"],
        logger=logger
    )

    logger.info(f"✅ 策略初始化完成：MA{strategy.short_window}/{strategy.long_window}")
    logger.info(
        f"   止损: {strategy.stop_loss_pct}%, 回撤止盈: {strategy.profit_take_ratio*100}%")

    # 4. 恢复或重建策略状态
    if manual_positions is not None:
        # 手动输入持仓模式
        strategy.positions = manual_positions
        strategy.signal_history = []  # 清空信号历史
        strategy.last_short_ma = None
        strategy.last_long_ma = None

        # 生成详细的持仓状态信息
        position_info = get_position_status_info()
        logger.info(f"📝 设置手动输入持仓: {position_info}")

        # 显示手动输入的持仓详情
        for i, pos in enumerate(strategy.positions):
            direction_emoji = "📈" if pos.direction == "long" else "📉"
            logger.info(
                f"   持仓{i+1}: {direction_emoji} {pos.direction} @ {pos.price:.4f} (数量: {pos.volume}, 开仓时间: {pos.datetime})")
            logger.info(
                f"           止损价: {pos.stop_loss_price:.4f} | 止盈价: {pos.take_profit_price:.4f}")
    elif saved_state:
        # 恢复保存的状态
        strategy.positions = saved_state['positions']
        strategy.signal_history = saved_state['signal_history']
        strategy.last_short_ma = saved_state['last_short_ma']
        strategy.last_long_ma = saved_state['last_long_ma']

        # 恢复交叉状态（兼容旧版本状态文件）
        strategy.last_cross_price = saved_state.get('last_cross_price', 0.0)
        strategy.last_cross_type = saved_state.get('last_cross_type', "")

        # 生成详细的持仓状态信息
        position_info = get_position_status_info()
        logger.info(f"📂 恢复持仓状态: {position_info}")

        # 显示交叉状态信息
        if strategy.last_cross_price > 0:
            logger.info(
                f"📂 恢复交叉状态: {strategy.last_cross_type}叉 @ {strategy.last_cross_price:.4f}")
        else:
            logger.info("📂 无历史交叉记录")

        # 显示恢复的持仓详情
        for i, pos in enumerate(strategy.positions):
            direction_emoji = "📈" if pos.direction == "long" else "📉"
            logger.info(
                f"   持仓{i+1}: {direction_emoji} {pos.direction} @ {pos.price:.4f} (开仓时间: {pos.datetime})")
    elif load_previous_state:
        # 只有在加载历史状态模式下才重建状态
        logger.info("🔍 未找到持仓状态文件，使用DataService历史数据重建...")
        rebuild_success = rebuild_strategy_state_from_dataservice()

        if not rebuild_success:
            logger.warning("⚠️ 无法重建策略状态，策略将从空仓开始")
    else:
        # 从零开始模式，完全跳过状态重建
        logger.info("🆕 从零开始模式：跳过历史数据重建，策略从空仓开始")
        logger.info("🆕 策略将等待实时K线数据开始计算均线和信号")

    # 5. 设置信号回调
    strategy.set_signal_callback(on_signal_generated)


def on_signal_generated(signal):
    """信号生成回调"""
    logger.info(
        f"🎯 策略信号: {signal['signal_type']} | 信号值: {signal['signal']} | 价格: {signal['price']:.4f}")
    logger.info(f"   原因: {signal['reason']}")

    # 保存持仓状态
    save_position_state()

    # 这里可以添加实际的交易执行逻辑
    if signal['signal'] == 1:
        logger.info("   ➡️ 执行做多操作")
        send_signal(signal['signal'], title)
    elif signal['signal'] == -1:
        logger.info("   ➡️ 执行做空操作")
        send_signal(signal['signal'], title)
    elif signal['signal'] == 0:
        logger.info("   ➡️ 执行平仓操作")
        send_signal(signal['signal'], title)


def create_websocket_connection():
    """创建WebSocket连接"""
    global ws_instance

    # # 设置环境变量（指向 SOCKS5 代理）
    # os.environ["ALL_PROXY"] = "socks5://127.0.0.1:7897"
    # os.environ["HTTPS_PROXY"] = "socks5://127.0.0.1:7897"

    url = "wss://fstream.binance.com/ws"
    ws_instance = websocket.WebSocketApp(
        url,
        on_message=on_message,
        on_error=on_error,
        on_close=on_close,
        on_open=on_open
    )

    return ws_instance


def reconnect_websocket():
    """重连WebSocket"""
    global reconnect_count, is_reconnecting

    if not WEBSOCKET_CONFIG["enable_reconnect"]:
        logger.info("🚫 自动重连已禁用")
        return False

    max_attempts = WEBSOCKET_CONFIG["max_reconnect_attempts"]
    if max_attempts > 0 and reconnect_count >= max_attempts:
        logger.error(f"❌ 已达到最大重连次数 ({max_attempts})，停止重连")
        return False

    is_reconnecting = True

    # 重连循环
    while WEBSOCKET_CONFIG["enable_reconnect"]:
        reconnect_count += 1

        # 检查是否超过最大重连次数
        if max_attempts > 0 and reconnect_count > max_attempts:
            logger.error(f"❌ 已达到最大重连次数 ({max_attempts})，停止重连")
            is_reconnecting = False
            return False

        # 计算重连延迟（指数退避）
        delay = min(
            WEBSOCKET_CONFIG["initial_reconnect_delay"] *
            (2 ** (reconnect_count - 1)),
            WEBSOCKET_CONFIG["max_reconnect_delay"]
        )

        logger.info(f"🔄 准备重连 (第{reconnect_count}次)，{delay}秒后开始...")
        time.sleep(delay)

        try:
            logger.info("🔗 尝试重新建立WebSocket连接...")
            ws = create_websocket_connection()
            ws.run_forever()
            # 如果到达这里，说明连接正常结束，退出重连循环
            break
        except KeyboardInterrupt:
            logger.info("⏹️ 用户中断重连")
            WEBSOCKET_CONFIG["enable_reconnect"] = False
            break
        except Exception as e:
            logger.error(f"❌ 重连失败: {e}")
            # 继续下一次重连尝试
            continue

    is_reconnecting = False
    return True


def on_message(ws, message):
    global strategy, reconnect_count, is_reconnecting

    # 重置重连计数器（收到消息说明连接正常）
    if reconnect_count > 0:
        logger.info(f"✅ 连接恢复正常，重置重连计数器 (之前重连{reconnect_count}次)")
        reconnect_count = 0
        is_reconnecting = False

    data = json.loads(message)

    # 只在K线闭合时才输出日志和处理信号
    if data.get('k', {}).get('x'):  # K线闭合
        kline = data['k']
        symbol = data.get('ps', 'Unknown')

        # 提取OHLCV数据
        open_price = float(kline['o'])
        high_price = float(kline['h'])
        low_price = float(kline['l'])
        close_price = float(kline['c'])
        volume = float(kline['v'])

        # 记录K线OHLCV信息
        logger.info(f"📊 K线闭合: {symbol}")
        logger.info(
            f"   OHLCV: O={open_price:.4f} H={high_price:.4f} L={low_price:.4f} C={close_price:.4f} V={volume:.2f}")

        if strategy:
            # 处理信号并获取均线信息
            signal = strategy.on_kline_update(data)

            # 获取最新的均线值
            ma_short = "N/A"
            ma_long = "N/A"
            if strategy.short_ma is not None and len(strategy.short_ma) > 0:
                ma_short = f"{strategy.short_ma.iloc[-1]:.4f}"
            if strategy.long_ma is not None and len(strategy.long_ma) > 0:
                ma_long = f"{strategy.long_ma.iloc[-1]:.4f}"

            # 记录均线信息
            logger.info(
                f"   均线: MA{strategy.short_window}={ma_short} | MA{strategy.long_window}={ma_long}")

            # 处理信号（策略内部已通过回调处理，这里只记录）
            if signal:
                signal_value = signal['signal']
                signal_type = signal['signal_type']
                logger.info(f"   信号: {signal_value} ({signal_type})")
                # 注意：不需要再次调用 on_signal_generated，策略内部已经通过回调处理了
            else:
                logger.info("   信号: None (无信号)")
                # 无信号时也保存状态
                save_position_state()
        else:
            logger.warning("   策略未初始化")


def on_error(ws, error):
    logger.error(f"❌ WebSocket错误: {error}")
    # 错误时也可能需要重连
    if WEBSOCKET_CONFIG["enable_reconnect"] and not is_reconnecting:
        logger.info("🔄 由于错误触发重连...")
        reconnect_websocket()


def on_close(ws, close_status_code, close_msg):
    global is_reconnecting

    # 记录关闭信息
    close_reason = f"状态码: {close_status_code}, 消息: {close_msg}" if close_status_code or close_msg else "未知原因"
    logger.info(f"🔌 WebSocket连接已关闭 ({close_reason})")

    # 连接关闭时保存状态
    save_position_state()

    # 如果启用了自动重连且不是正在重连中，则尝试重连
    if WEBSOCKET_CONFIG["enable_reconnect"] and not is_reconnecting:
        logger.info("🔄 连接关闭，准备自动重连...")
        reconnect_websocket()
    else:
        logger.info("🚫 自动重连已禁用或正在重连中")


def on_open(ws):
    global reconnect_count, is_reconnecting

    logger.info("🔗 WebSocket连接已建立")

    # 如果是重连成功，记录信息
    if reconnect_count > 0:
        logger.info(f"✅ 重连成功 (第{reconnect_count}次尝试)")

    # 重置重连状态
    is_reconnecting = False

    # 订阅ETHUSDT永续合约15分钟K线数据
    payload = {
        "method": "SUBSCRIBE",
        "params": [f"{WS_DATA_COFIG['symbol']}_perpetual@continuousKline_{WS_DATA_COFIG['interval']}"],
        "id": 1
    }
    ws.send(json.dumps(payload))
    logger.info(
        f"📡 已订阅: {WS_DATA_COFIG['symbol']}_perpetual@continuousKline_{WS_DATA_COFIG['interval']}")

    # 显示重连配置信息
    if WEBSOCKET_CONFIG["enable_reconnect"]:
        logger.info(
            f"🔄 自动重连已启用 (最大重连次数: {WEBSOCKET_CONFIG['max_reconnect_attempts']})")
        logger.info("💓 心跳机制: websocket-client库自动处理ping/pong")
    else:
        logger.info("🚫 自动重连已禁用")


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='无敌的策略')
    parser.add_argument(
        '--load-state',
        type=str,
        choices=['true', 'false', 'manual'],
        default='false',
        help='启动模式 (true: 加载历史状态, false: 从零开始, manual: 手动传入持仓)'
    )
    parser.add_argument(
        '--position-data',
        type=str,
        help='持仓数据JSON字符串，格式: [{"direction":"long","price":3500.0,"volume":1.0}]'
    )
    return parser.parse_args()


def main():
    """主函数"""
    global title

    # 初始化全局变量
    title = SIGNAL_CONFIG["title"]

    logger.info("🚀 启动MA交叉策略交易系统...")
    logger.info("="*50)

    # 根据配置决定启动模式
    manual_positions = None
    manual_position_data = None
    load_previous_state = False

    if STARTUP_MODE == 'manual':
        logger.info("📊 启动模式: 手动设置持仓")
        manual_position_data = MANUAL_POSITIONS
        logger.info(f"✅ 使用配置的持仓数据: {len(manual_position_data)}个持仓")
    elif STARTUP_MODE == 'true':
        logger.info("📂 启动模式: 加载历史状态")
        load_previous_state = True
    else:  # 'false'
        logger.info("🆕 启动模式: 从零开始")
        load_previous_state = False

    # 如果选择从零开始，清理状态文件
    if STARTUP_MODE == 'false' and POSITION_STATE_FILE.exists():
        try:
            POSITION_STATE_FILE.unlink()
            logger.info("🗑️ 已清理历史状态文件")
        except Exception as e:
            logger.error(f"❌ 清理状态文件失败: {e}")

    # 初始化系统
    init_system(load_previous_state, manual_positions, manual_position_data)

    # 创建并启动WebSocket连接
    try:
        logger.info("🔄 启动WebSocket连接...")
        ws = create_websocket_connection()
        ws.run_forever()
    except KeyboardInterrupt:
        logger.info("\n⏹️ 用户中断")
        # 禁用自动重连，避免用户中断后继续重连
        WEBSOCKET_CONFIG["enable_reconnect"] = False
    except Exception as e:
        logger.error(f"❌ 系统错误: {e}")
        # 如果启用了自动重连，尝试重连
        if WEBSOCKET_CONFIG["enable_reconnect"]:
            logger.info("🔄 由于系统错误触发重连...")
            reconnect_websocket()
    finally:
        # 程序退出时保存状态
        save_position_state()
        logger.info("🛑 系统已停止")


if __name__ == "__main__":
    main()
